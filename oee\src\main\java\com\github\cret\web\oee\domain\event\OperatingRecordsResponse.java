package com.github.cret.web.oee.domain.event;

import java.util.List;

public class OperatingRecordsResponse {

	private String lineCode;

	private List<OperatingRecordDto> records;

	public OperatingRecordsResponse(String lineCode, List<OperatingRecordDto> records) {
		this.lineCode = lineCode;
		this.records = records;
	}

	// Getters and Setters
	public String getLineCode() {
		return lineCode;
	}

	public void setLineCode(String lineCode) {
		this.lineCode = lineCode;
	}

	public List<OperatingRecordDto> getRecords() {
		return records;
	}

	public void setRecords(List<OperatingRecordDto> records) {
		this.records = records;
	}

}
