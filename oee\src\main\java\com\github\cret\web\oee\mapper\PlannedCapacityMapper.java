package com.github.cret.web.oee.mapper;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.github.cret.web.oee.document.PlannedCapacity;
import com.github.cret.web.oee.domain.capacity.PlannedCapacityExcelDto;

@Mapper(componentModel = "spring")
public interface PlannedCapacityMapper {

	@Mapping(target = "id", ignore = true)
	PlannedCapacity toEntity(PlannedCapacityExcelDto dto);

	PlannedCapacityExcelDto toExcelDto(PlannedCapacity entity);

	List<PlannedCapacityExcelDto> toExcelDto(List<PlannedCapacity> entity);

}