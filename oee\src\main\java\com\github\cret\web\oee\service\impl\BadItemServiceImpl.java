package com.github.cret.web.oee.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.github.cret.web.oee.document.BadItemDoc;
import com.github.cret.web.oee.repository.BadItemRepository;
import com.github.cret.web.oee.service.BadItemService;

@Service
public class BadItemServiceImpl implements BadItemService {

	private static final Logger logger = LoggerFactory.getLogger(BadItemServiceImpl.class);

	private final BadItemRepository badItemRepository;

	public BadItemServiceImpl(BadItemRepository badItemRepository) {
		this.badItemRepository = badItemRepository;
	}

	@Override
	public Optional<BadItemDoc> findByBadItemId(String badItemId) {
		try {
			return badItemRepository.findByBadItemId(badItemId);
		}
		catch (Exception e) {
			logger.error("查询不良项目ID {} 失败", badItemId, e);
			return Optional.empty();
		}
	}

	@Override
	public List<BadItemDoc> findByBadItemIds(List<String> badItemIds) {
		try {
			return badItemRepository.findByBadItemIdIn(badItemIds);
		}
		catch (Exception e) {
			logger.error("批量查询不良项目ID失败", e);
			return List.of();
		}
	}

	@Override
	public Map<String, String> getBadItemIdToNameMap(List<String> badItemIds) {
		try {
			List<BadItemDoc> badItems = findByBadItemIds(badItemIds);
			return badItems.stream()
				.collect(Collectors.toMap(BadItemDoc::getBadItemId, BadItemDoc::getBadItemName,
						(existing, replacement) -> existing // 如果有重复，保留第一个
				));
		}
		catch (Exception e) {
			logger.error("获取不良项目ID到名称映射失败", e);
			return Map.of();
		}
	}

	@Override
	public List<BadItemDoc> findByBadTypeId(String badTypeId) {
		try {
			return badItemRepository.findByBadTypeId(badTypeId);
		}
		catch (Exception e) {
			logger.error("查询不良类型ID {} 失败", badTypeId, e);
			return List.of();
		}
	}

	@Override
	public List<BadItemDoc> findAll() {
		try {
			return badItemRepository.findAll();
		}
		catch (Exception e) {
			logger.error("查询所有不良项目失败", e);
			return List.of();
		}
	}

	@Override
	public BadItemDoc save(BadItemDoc badItem) {
		try {
			return badItemRepository.save(badItem);
		}
		catch (Exception e) {
			logger.error("保存不良项目失败：{}", badItem, e);
			throw new RuntimeException("保存不良项目失败", e);
		}
	}

	@Override
	public List<BadItemDoc> saveAll(List<BadItemDoc> badItems) {
		try {
			return badItemRepository.saveAll(badItems);
		}
		catch (Exception e) {
			logger.error("批量保存不良项目失败", e);
			throw new RuntimeException("批量保存不良项目失败", e);
		}
	}

	@Override
	public List<BadItemDoc> findByBadItemNameContaining(String badItemName) {
		try {
			return badItemRepository.findByBadItemNameContaining(badItemName);
		}
		catch (Exception e) {
			logger.error("模糊查询不良项目名称 {} 失败", badItemName, e);
			return List.of();
		}
	}

	@Override
	public boolean existsByBadItemId(String badItemId) {
		try {
			return badItemRepository.findByBadItemId(badItemId).isPresent();
		}
		catch (Exception e) {
			logger.error("检查不良项目ID {} 是否存在失败", badItemId, e);
			return false;
		}
	}

	@Override
	public void deleteByBadItemId(String badItemId) {
		try {
			Optional<BadItemDoc> badItem = badItemRepository.findByBadItemId(badItemId);
			if (badItem.isPresent()) {
				badItemRepository.delete(badItem.get());
				logger.info("删除不良项目ID {} 成功", badItemId);
			}
			else {
				logger.warn("不良项目ID {} 不存在，无法删除", badItemId);
			}
		}
		catch (Exception e) {
			logger.error("删除不良项目ID {} 失败", badItemId, e);
			throw new RuntimeException("删除不良项目失败", e);
		}
	}

	@Override
	public Map<String, Long> getBadTypeStatistics() {
		try {
			List<BadItemDoc> allBadItems = badItemRepository.findAll();
			return allBadItems.stream().collect(Collectors.groupingBy(BadItemDoc::getBadTypeId, Collectors.counting()));
		}
		catch (Exception e) {
			logger.error("获取不良类型统计信息失败", e);
			return Map.of();
		}
	}

}
