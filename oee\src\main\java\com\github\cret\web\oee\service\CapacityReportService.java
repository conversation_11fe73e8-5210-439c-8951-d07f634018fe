package com.github.cret.web.oee.service;

import java.util.List;

import com.github.cret.web.oee.domain.capacity.CapacityReportWithLine;

import jakarta.servlet.http.HttpServletResponse;

public interface CapacityReportService {

	/**
	 * 获取产能报表数据
	 * @param workDate
	 * @return
	 */
	List<CapacityReportWithLine> getCapacityReport(String workDate, int interval);

	/**
	 * 导出产能报表
	 * @param workDate
	 * @param response
	 */
	void exportCapacityReport(String workDate, int interval, HttpServletResponse response);

}
