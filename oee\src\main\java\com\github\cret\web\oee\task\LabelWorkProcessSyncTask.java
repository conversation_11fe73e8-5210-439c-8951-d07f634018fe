package com.github.cret.web.oee.task;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.github.cret.web.oee.service.LabelWorkProcessWithQcBadItemsService;

/**
 * 标签工艺过程分析与QC不良项目组合数据同步定时任务
 */
@Service
public class LabelWorkProcessSyncTask {

	private static final Logger logger = LoggerFactory.getLogger(LabelWorkProcessSyncTask.class);

	private final LabelWorkProcessWithQcBadItemsService labelWorkProcessWithQcBadItemsService;

	public LabelWorkProcessSyncTask(LabelWorkProcessWithQcBadItemsService labelWorkProcessWithQcBadItemsService) {
		this.labelWorkProcessWithQcBadItemsService = labelWorkProcessWithQcBadItemsService;
	}

	/**
	 * 每10分钟执行一次，同步所有线体当天的标签工艺过程分析与QC不良项目组合数据
	 */
	@Scheduled(fixedRate = 1800000) // 1800,000 毫秒 = 30 分钟
	public void syncLabelWorkProcessAnalysisWithQcBadItems() {
		try {
			logger.info("开始执行标签工艺过程分析与QC不良项目组合数据同步定时任务");

			int totalSyncCount = labelWorkProcessWithQcBadItemsService.syncAllLinesCurrentDayData();

			logger.info("标签工艺过程分析与QC不良项目组合数据同步定时任务执行完成，共同步 {} 条数据", totalSyncCount);
		}
		catch (Exception e) {
			logger.error("标签工艺过程分析与QC不良项目组合数据同步定时任务执行失败", e);
		}
	}

	/**
	 * 每天凌晨2点执行一次，同步所有线体历史数据（两天前到现在）
	 */
	@Scheduled(cron = "0 0 2 * * ?")
	public void syncHistoricalLabelWorkProcessAnalysisWithQcBadItems() {
		try {
			logger.info("开始执行标签工艺过程分析与QC不良项目组合历史数据同步定时任务");

			int totalSyncCount = labelWorkProcessWithQcBadItemsService.syncAllLinesHistoricalData();

			logger.info("标签工艺过程分析与QC不良项目组合历史数据同步定时任务执行完成，共同步 {} 条数据", totalSyncCount);
		}
		catch (Exception e) {
			logger.error("标签工艺过程分析与QC不良项目组合历史数据同步定时任务执行失败", e);
		}
	}

}
