package com.github.cret.web.oee.domain.event;

import java.util.Date;

public class OperatingRecordDto {

	private Date startTime;

	private Date endTime;

	public OperatingRecordDto(Date startTime, Date endTime) {
		this.startTime = startTime;
		this.endTime = endTime;
	}

	// Getters and Setters
	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

}
