package com.github.cret.web.oee.document;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Document(collection = "t_unmet_reason")
public class UnmetReason {

	@Id
	private String id;

	@Field("start_work_time")
	private String startWorkTime;

	@Field("end_work_time")
	private String endWorkTime;

	// @Field("work_time")
	// private String workTime;

	@Field("line_id")
	private String lineId;

	// @Field("work_date")
	// private String workDate;

	// @Field("hour_of_day")
	// private Integer hourOfDay;

	@Field("reason")
	private String reason;

	@Field("user_id")
	private String userId;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getStartWorkTime() {
		return startWorkTime;
	}

	public void setStartWorkTime(String startWorkTime) {
		this.startWorkTime = startWorkTime;
	}

	public String getEndWorkTime() {
		return endWorkTime;
	}

	public void setEndWorkTime(String endWorkTime) {
		this.endWorkTime = endWorkTime;
	}

	public String getLineId() {
		return lineId;
	}

	public void setLineId(String lineId) {
		this.lineId = lineId;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

}
