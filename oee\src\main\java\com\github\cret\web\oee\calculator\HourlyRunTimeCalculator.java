package com.github.cret.web.oee.calculator;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;

import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.domain.analyze.HourlyRunTimeGroup;
import com.github.cret.web.oee.utils.DocumentUtil;
import com.github.cret.web.oee.utils.OeeUtil;

/**
 * 计算每小时的产品运行时间
 */
public class HourlyRunTimeCalculator {

	public static List<HourlyRunTimeGroup> getHourlyRunTimeGroup(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		return switch (device.getType()) {
			case smt_npm_reporter -> getNpmSmtHourlyRunTime(device, query, mongoTemplate);
			case smt_npm_reporter2 -> getNpmSmtHourlyRunTime(device, query, mongoTemplate);
			case smt_samsung -> getSamsungSmtHourlyRunTime(device, query, mongoTemplate);
			case smt_yamaha -> getYamahaSmtHourlyRunTime(device, query, mongoTemplate);
			default -> throw new UnsupportedOperationException("该设备类型未实现每小时实际产出查询");
		};
	}

	/**
	 * 获取松下贴片机的每小时产品运行时间
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB操作模板
	 * @return 运行时间列表
	 */
	public static List<HourlyRunTimeGroup> getNpmSmtHourlyRunTime(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {

		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建聚合管道
		List<Document> pipeline = new ArrayList<>();

		// 1. 匹配时间范围和产品型号
		Document matchDoc = new Document("time",
				new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchDoc.append("productmodel", query.getProductModel());
		}
		pipeline.add(new Document("$match", matchDoc));

		// 2. 按时间排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// 3. 添加前一个值字段
		pipeline.add(new Document("$setWindowFields",
				new Document().append("sortBy", new Document("time", 1).append("totaltime", -1))
					.append("output", new Document().append("previousValue",
							new Document("$shift", new Document().append("output", "$totaltime").append("by", -1))))));

		// 4. 计算运行时间差值
		pipeline.add(new Document("$addFields",
				new Document("result", new Document("$cond",
						new Document().append("if", new Document("$gte", Arrays.asList("$totaltime", "$previousValue")))
							.append("then", new Document("$subtract", Arrays.asList("$totaltime", "$previousValue")))
							.append("else", "$totaltime")))));

		// 5. 过滤掉空值
		pipeline.add(new Document("$match", new Document("result", new Document("$ne", null))));

		// 6. 添加小时分组字段（带时区）
		pipeline.add(new Document("$addFields",
				new Document("hour",
						new Document("$dateToString",
								new Document().append("format", "%Y-%m-%d %H:00:00")
									.append("date", "$time")
									.append("timezone", "Asia/Shanghai")))));

		// 7. 按产品型号和小时分组，同时保留最早的时间信息
		pipeline.add(new Document("$group",
				new Document()
					.append("_id", new Document().append("productmodel", "$productmodel").append("hour", "$hour"))
					.append("total", new Document("$sum", "$result"))
					.append("firstTime", new Document("$min", "$time")) // 添加最早时间
		));

		// 8. 按时间和产品型号排序
		pipeline.add(new Document("$sort", new Document("_id.hour", 1).append("firstTime", 1) // 在同一小时内按照最早时间排序
			.append("_id.productmodel", 1)));

		// 执行聚合查询
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		// 转换结果
		List<HourlyRunTimeGroup> hourlyRunTimes = new ArrayList<>();
		for (Document doc : results) {
			Document idDoc = (Document) doc.get("_id");
			HourlyRunTimeGroup hourlyRunTime = new HourlyRunTimeGroup();
			hourlyRunTime.setProductModel(idDoc.getString("productmodel"));
			hourlyRunTime.setHour(idDoc.getString("hour"));
			hourlyRunTime.setRunTime(doc.getDouble("total"));
			hourlyRunTime.setFirstTime(doc.getDate("firstTime"));
			hourlyRunTimes.add(hourlyRunTime);
		}

		return hourlyRunTimes;
	}

	/**
	 * 获取三星贴片机的每小时产品运行时间
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static List<HourlyRunTimeGroup> getSamsungSmtHourlyRunTime(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合名称
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建聚合管道
		List<Document> pipeline = new ArrayList<>();

		// 1. 匹配时间范围和产品型号
		Document matchDoc = new Document("time",
				new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));
		matchDoc.append("normalflag", true);
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchDoc.append("productmodel", query.getProductModel());
		}
		pipeline.add(new Document("$match", matchDoc));

		// 2. 按时间排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// 3. 计算 totaltime（runtime + stoptime + idletime）
		pipeline.add(new Document("$addFields", new Document("totaltime", new Document("$add",
				Arrays.asList("$samsungindex.runtime", "$samsungindex.stoptime", "$samsungindex.idletime")))));

		// 4. 添加前一个值字段（通过窗口函数）
		pipeline.add(new Document("$setWindowFields",
				new Document().append("sortBy", new Document("time", 1).append("totaltime", -1))
					.append("output", new Document().append("previousValue",
							new Document("$shift", new Document().append("output", "$totaltime").append("by", -1))))));

		// 5. 计算运行时间差值
		pipeline.add(new Document("$addFields",
				new Document("result", new Document("$cond",
						new Document().append("if", new Document("$gte", Arrays.asList("$totaltime", "$previousValue")))
							.append("then", new Document("$subtract", Arrays.asList("$totaltime", "$previousValue")))
							.append("else", "$totaltime")))));

		// 6. 过滤掉空值
		pipeline.add(new Document("$match", new Document("result", new Document("$ne", null))));

		// 7. 添加小时分组字段（带时区）
		pipeline.add(new Document("$addFields",
				new Document("hour",
						new Document("$dateToString",
								new Document().append("format", "%Y-%m-%d %H:00:00")
									.append("date", "$time")
									.append("timezone", "Asia/Shanghai")))));

		// 8. 按产品型号和小时分组，同时保留最早的时间信息
		pipeline.add(new Document("$group",
				new Document()
					.append("_id", new Document().append("productmodel", "$productmodel").append("hour", "$hour"))
					.append("total", new Document("$sum", "$result"))
					.append("firstTime", new Document("$min", "$time")) // 添加最早时间
		));

		// 9. 按时间和产品型号排序
		pipeline.add(new Document("$sort", new Document("_id.hour", 1).append("firstTime", 1) // 在同一小时内按照最早时间排序
			.append("_id.productmodel", 1)));

		// 执行聚合查询
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		// 转换结果
		List<HourlyRunTimeGroup> hourlyRunTimes = new ArrayList<>();
		for (Document doc : results) {
			Document idDoc = (Document) doc.get("_id");
			HourlyRunTimeGroup hourlyRunTime = new HourlyRunTimeGroup();
			hourlyRunTime.setProductModel(idDoc.getString("productmodel"));
			hourlyRunTime.setHour(idDoc.getString("hour"));
			Object total = doc.get("total");
			double totalRunTime = DocumentUtil.getDouble(total);
			hourlyRunTime.setRunTime(totalRunTime);
			hourlyRunTime.setFirstTime(doc.getDate("firstTime"));
			hourlyRunTimes.add(hourlyRunTime);
		}

		return hourlyRunTimes;
	}

	/**
	 * 获取雅马哈贴片机的每小时产品运行时间
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static List<HourlyRunTimeGroup> getYamahaSmtHourlyRunTime(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建聚合管道
		List<Document> pipeline = new ArrayList<>();

		// 1. 匹配时间范围和产品型号
		Document matchDoc = new Document("time",
				new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchDoc.append("productmodel", query.getProductModel());
		}
		pipeline.add(new Document("$match", matchDoc));

		// 2. 按时间排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// 3. 添加前一个值字段
		pipeline.add(new Document("$setWindowFields",
				new Document().append("sortBy", new Document("time", 1).append("totaltime", -1))
					.append("output", new Document().append("previousValue",
							new Document("$shift", new Document().append("output", "$totaltime").append("by", -1))))));

		// 4. 计算运行时间差值
		pipeline.add(new Document("$addFields",
				new Document("result", new Document("$cond",
						new Document().append("if", new Document("$gte", Arrays.asList("$totaltime", "$previousValue")))
							.append("then", new Document("$subtract", Arrays.asList("$totaltime", "$previousValue")))
							.append("else", "$totaltime")))));

		// 5. 过滤掉空值
		pipeline.add(new Document("$match", new Document("result", new Document("$ne", null))));

		// 6. 添加小时分组字段（带时区）
		pipeline.add(new Document("$addFields",
				new Document("hour",
						new Document("$dateToString",
								new Document().append("format", "%Y-%m-%d %H:00:00")
									.append("date", "$time")
									.append("timezone", "Asia/Shanghai")))));

		// 7. 按产品型号和小时分组，同时保留最早的时间信息
		pipeline.add(new Document("$group",
				new Document()
					.append("_id", new Document().append("productmodel", "$productmodel").append("hour", "$hour"))
					.append("total", new Document("$sum", "$result"))
					.append("firstTime", new Document("$min", "$time")) // 添加最早时间
		));

		// 8. 按时间和产品型号排序
		pipeline.add(new Document("$sort", new Document("_id.hour", 1).append("firstTime", 1) // 在同一小时内按照最早时间排序
			.append("_id.productmodel", 1)));

		// 执行聚合查询
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		// 转换结果
		List<HourlyRunTimeGroup> hourlyRunTimes = new ArrayList<>();
		for (Document doc : results) {
			Document idDoc = (Document) doc.get("_id");
			HourlyRunTimeGroup hourlyRunTime = new HourlyRunTimeGroup();
			hourlyRunTime.setProductModel(idDoc.getString("productmodel"));
			hourlyRunTime.setHour(idDoc.getString("hour"));
			hourlyRunTime.setRunTime(doc.getDouble("total"));
			hourlyRunTime.setFirstTime(doc.getDate("firstTime"));
			hourlyRunTimes.add(hourlyRunTime);
		}

		return hourlyRunTimes;
	}

}
