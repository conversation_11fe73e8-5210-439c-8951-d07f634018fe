package com.github.cret.web.oee.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.bson.Document;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.ExampleMatcher.GenericPropertyMatchers;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.ArithmeticOperators;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.aggregation.SortOperation;
import org.springframework.data.mongodb.core.aggregation.StringOperators;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.document.PlannedCapacity;
import com.github.cret.web.oee.document.analyze.TheoreticalOutput;
import com.github.cret.web.oee.domain.analyze.DeviceProductModelKey;
import com.github.cret.web.oee.domain.theoreticalOutput.TheoreticalOutputSample;
import com.github.cret.web.oee.enums.DeviceCategory;
import com.github.cret.web.oee.excel.TheoreticalOutputListener;
import com.github.cret.web.oee.repository.DeviceRepository;
import com.github.cret.web.oee.repository.TheoreticalOutputRepository;
import com.github.cret.web.oee.service.TheoreticalOutputService;
import com.github.cret.web.oee.utils.OeeUtil;

@Service
public class TheoreticalOutputServiceImpl implements TheoreticalOutputService {

	private final TheoreticalOutputRepository repository;

	private final DeviceRepository deviceRepository;

	private final MongoTemplate mongoTemplate;

	public TheoreticalOutputServiceImpl(TheoreticalOutputRepository repository, DeviceRepository deviceRepository,
			MongoTemplate mongoTemplate) {
		this.repository = repository;
		this.deviceRepository = deviceRepository;
		this.mongoTemplate = mongoTemplate;
	}

	@Override
	public TheoreticalOutput save(TheoreticalOutput theoreticalOutput) {
		return mongoTemplate.save(theoreticalOutput);
	}

	@Override
	public TheoreticalOutput findById(String id) {
		return mongoTemplate.findById(id, TheoreticalOutput.class);
	}

	@Override
	public PageList<TheoreticalOutput> page(PageableParam<TheoreticalOutput> param) {
		ExampleMatcher matcher = ExampleMatcher.matching()
			.withIgnoreNullValues()
			.withMatcher("lineCode", GenericPropertyMatchers.exact())
			.withMatcher("deviceCode", GenericPropertyMatchers.contains())
			.withMatcher("deviceType", GenericPropertyMatchers.contains())
			.withMatcher("productModel", GenericPropertyMatchers.contains());
		Example<TheoreticalOutput> example = Example.of(param.getSearchParams(), matcher);
		return PageList.fromPage(repository.findAll(example, param.getPageRequest()));
	}

	@Override
	public List<TheoreticalOutput> findList(TheoreticalOutput param) {
		ExampleMatcher matcher = ExampleMatcher.matching()
			.withIgnoreNullValues()
			.withMatcher("lineCode", GenericPropertyMatchers.exact())
			.withMatcher("deviceCode", GenericPropertyMatchers.contains())
			.withMatcher("deviceType", GenericPropertyMatchers.contains())
			.withMatcher("productModel", GenericPropertyMatchers.contains());
		Example<TheoreticalOutput> example = Example.of(param, matcher);
		return repository.findAll(example);
	}

	@Override
	public List<TheoreticalOutput> findAll() {
		return mongoTemplate.findAll(TheoreticalOutput.class);
	}

	@Override
	public void deleteById(String id) {
		TheoreticalOutput theoreticalOutput = findById(id);
		if (theoreticalOutput != null) {
			mongoTemplate.remove(theoreticalOutput);
		}
	}

	@Override
	public void batchDelete(List<String> ids) {
		if (ids == null || ids.isEmpty()) {
			return;
		}

		Query query = Query.query(Criteria.where("_id").in(ids));
		mongoTemplate.remove(query, TheoreticalOutput.class);
	}

	@Override
	public void importExcel(MultipartFile file) throws IOException {
		EasyExcel.read(file.getInputStream(), TheoreticalOutput.class, new TheoreticalOutputListener(repository))
			.sheet()
			.doRead();
	}

	@Override
	public TheoreticalOutput findByDeviceCodeAndProductModel(String deviceCode, String productModel) {
		if (!StringUtils.hasText(deviceCode) || !StringUtils.hasText(productModel)) {
			return null;
		}

		TheoreticalOutput param = new TheoreticalOutput();
		param.setDeviceCode(deviceCode);
		param.setProductModel(productModel);

		ExampleMatcher matcher = ExampleMatcher.matching()
			.withIgnoreNullValues()
			.withMatcher("deviceCode", GenericPropertyMatchers.exact())
			.withMatcher("productModel", GenericPropertyMatchers.exact());

		Example<TheoreticalOutput> example = Example.of(param, matcher);
		return repository.findOne(example).orElse(null);
	}

	@Override
	public TheoreticalOutput findByDeviceCodeAndProductModelContaining(String deviceCode, String productModel) {
		if (!StringUtils.hasText(deviceCode) || !StringUtils.hasText(productModel)) {
			return null;
		}

		List<String> productModelCandidates = generateProductModelCandidates(productModel);

		for (String candidate : productModelCandidates) {
			TheoreticalOutput param = new TheoreticalOutput();
			param.setDeviceCode(deviceCode);
			param.setProductModel(candidate);

			ExampleMatcher matcher = ExampleMatcher.matching()
				.withIgnoreNullValues()
				.withMatcher("deviceCode", GenericPropertyMatchers.exact())
				.withMatcher("productModel", GenericPropertyMatchers.exact());

			Example<TheoreticalOutput> example = Example.of(param, matcher);
			Optional<TheoreticalOutput> result = repository.findOne(example);
			if (result.isPresent()) {
				return result.get();
			}
		}
		return null;
	}

	/**
	 * 生成候选列表
	 * @param productModel
	 * @return
	 */
	private List<String> generateProductModelCandidates(String productModel) {
		List<String> candidates = new ArrayList<>();
		List<String> tempList = new ArrayList<>();
		String current = productModel;

		tempList.add(current);
		while (true) {
			String next = current.replaceAll("-\\d+$", "");
			if (next.equals(current)) {
				break;
			}
			tempList.add(next);
			current = next;
		}

		candidates.addAll(tempList);
		return candidates;
	}

	@Override
	public String getMaxCtDeviceByProductModelAndLineCode(String lineCode, String productModel) {
		// 依据生产线编码获取所有的设备
		List<Device> devices = deviceRepository.findByLineIdAndCategory(lineCode, DeviceCategory.SMT);
		devices = devices.stream().filter(device -> device.getEnable() == 1).toList();

		if (devices.isEmpty()) {
			return null;
		}

		List<String> codes = devices.stream().map(Device::getCode).collect(Collectors.toList());

		// 获取理论ct信息
		List<TheoreticalOutput> theoreticalOutputs = repository.findByDeviceCodeInAndProductModel(codes, productModel);

		if (theoreticalOutputs.isEmpty()) {
			return null;
		}

		// 获取最大的ct设备
		return theoreticalOutputs.stream()
			.max(Comparator.comparing(TheoreticalOutput::getCt))
			.map(TheoreticalOutput::getDeviceCode)
			.orElse(null);
	}

	@Override
	public Map<DeviceProductModelKey, TheoreticalOutput> batchFindByDeviceCodesAndProductModels(
			List<DeviceProductModelKey> keys) {

		if (keys == null || keys.isEmpty()) {
			return Collections.emptyMap();
		}

		// 构建查询条件
		Query query = buildBatchQuery(keys);

		// 执行查询
		List<TheoreticalOutput> results = mongoTemplate.find(query, TheoreticalOutput.class);

		// 转换为 Map
		return results.stream()
			.collect(Collectors.toMap(output -> new DeviceProductModelKey(output.getDeviceCode(),
					normalizeProductModel(output.getProductModel())), output -> output));
	}

	/**
	 * 构建批量查询条件
	 */
	private Query buildBatchQuery(List<DeviceProductModelKey> keys) {
		List<Criteria> criteriaList = keys.stream()
			.map(key -> Criteria.where("deviceCode")
				.is(key.getDeviceCode())
				.and("productModel")
				.is(normalizeProductModel(key.getProductModel())))
			.collect(Collectors.toList());

		return Query.query(new Criteria().orOperator(criteriaList));
	}

	/**
	 * 统一处理产品型号的空值情况
	 */
	private String normalizeProductModel(String productModel) {
		return productModel != null ? productModel : "暂无产品";
	}

	@Override
	public Double findMaxCtByProductModelAndLineCode(String productModel, String lineCode) {
		// 依据生产线编码获取所有的设备
		List<Device> devices = deviceRepository.findByLineId(lineCode);
		// devices = devices.stream().filter(device -> device.getEnable() == 1).toList();

		if (devices.isEmpty()) {
			return 0.0;
		}

		List<String> codes = devices.stream().map(Device::getCode).collect(Collectors.toList());

		// 获取理论ct信息
		List<TheoreticalOutput> theoreticalOutputs = repository.findByDeviceCodeInAndProductModel(codes, productModel);

		if (theoreticalOutputs.isEmpty()) {
			return 0.0;
		}

		// 返回最大的ct值
		return theoreticalOutputs.stream().map(TheoreticalOutput::getCt).max(Double::compareTo).orElse(null);
	}

	@Override
	public List<PlannedCapacity> findPlannedCapacityByGrouping(String lineCode, String productModel) {
		List<AggregationOperation> pipeline = new ArrayList<>();

		Criteria matchCriteria = new Criteria();
		boolean criteriaAdded = false;

		if (lineCode != null && !lineCode.isEmpty()) {
			matchCriteria.and("lineCode").is(lineCode);
			criteriaAdded = true;
		}

		if (productModel != null && !productModel.isEmpty()) {
			matchCriteria.and("productModel").is(productModel);
			criteriaAdded = true;
		}

		if (criteriaAdded) {
			pipeline.add(Aggregation.match(matchCriteria));
		}

		GroupOperation groupOperation = Aggregation.group("lineCode", "productModel")
			.max("ct")
			.as("maxCt")
			.first("flatNumber")
			.as("flatNumber");
		pipeline.add(groupOperation);

		ProjectionOperation projectOperation = Aggregation.project()
			.andExclude("_id")
			.and(StringOperators.Concat.valueOf("$_id.lineCode").concat("_").concat("$_id.productModel"))
			.as("id")
			.and("$_id.lineCode")
			.as("lineCode")
			.and("$_id.productModel")
			.as("productModel")
			.and(ArithmeticOperators.Floor.floorValueOf(
					ArithmeticOperators.Multiply.valueOf(ArithmeticOperators.Divide.valueOf(3600).divideBy("$maxCt"))
						.multiplyBy("$flatNumber")))
			.as("plannedQuantity");
		pipeline.add(projectOperation);

		SortOperation sortOperation = Aggregation.sort(Sort.by(Sort.Direction.ASC, "lineCode", "productModel"));
		pipeline.add(sortOperation);

		TypedAggregation<PlannedCapacity> aggregation = Aggregation.newAggregation(PlannedCapacity.class, pipeline);

		AggregationResults<PlannedCapacity> results = mongoTemplate.aggregate(aggregation, "theoretical_output",
				PlannedCapacity.class);

		return results.getMappedResults();
	}

	@Override
	public List<PlannedCapacity> findPlannedCapacityByGrouping(String lineCode, List<String> productModels) {
		List<AggregationOperation> pipeline = new ArrayList<>();

		// 构建匹配条件
		Criteria matchCriteria = new Criteria();
		boolean criteriaAdded = false;

		if (lineCode != null && !lineCode.isEmpty()) {
			matchCriteria.and("lineCode").is(lineCode);
			criteriaAdded = true;
		}

		if (productModels != null && !productModels.isEmpty()) {
			matchCriteria.and("productModel").in(productModels);
			criteriaAdded = true;
		}

		if (criteriaAdded) {
			pipeline.add(Aggregation.match(matchCriteria));
		}

		// 按lineCode和productModel分组，取最大ct
		GroupOperation groupOperation = Aggregation.group("lineCode", "productModel")
			.max("ct")
			.as("maxCt")
			.first("flatNumber")
			.as("flatNumber");
		pipeline.add(groupOperation);

		// 投影操作，计算计划产能
		ProjectionOperation projectOperation = Aggregation.project()
			.andExclude("_id")
			.and(StringOperators.Concat.valueOf("$_id.lineCode").concat("_").concat("$_id.productModel"))
			.as("id")
			.and("$_id.lineCode")
			.as("lineCode")
			.and("$_id.productModel")
			.as("productModel")
			.and(ArithmeticOperators.Floor.floorValueOf(
					ArithmeticOperators.Multiply.valueOf(ArithmeticOperators.Divide.valueOf(3600).divideBy("$maxCt"))
						.multiplyBy("$flatNumber")))
			.as("plannedQuantity");
		pipeline.add(projectOperation);

		// 执行聚合查询
		TypedAggregation<PlannedCapacity> aggregation = Aggregation.newAggregation(PlannedCapacity.class, pipeline);

		AggregationResults<PlannedCapacity> results = mongoTemplate.aggregate(aggregation, "theoretical_output",
				PlannedCapacity.class);

		return results.getMappedResults();
	}

	@Override
	public List<TheoreticalOutputSample> getTheoreticalOutputSamples(Device device) {
		// 1. 获取设备的生产数据集合名称
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());
		if (!mongoTemplate.collectionExists(collectionName)) {
			return Collections.emptyList();
		}

		// 2. 使用聚合查询获取所有不重复的 productmodel
		Aggregation aggregation = Aggregation.newAggregation(Aggregation.group("productmodel"),
				Aggregation.project().and("_id").as("productModel").andExclude("_id"));

		AggregationResults<Document> aggregationResults = mongoTemplate.aggregate(aggregation, collectionName,
				Document.class);

		// 提取所有非空的产品型号
		List<String> allProductModels = aggregationResults.getMappedResults()
			.stream()
			.map(doc -> doc.getString("productModel"))
			.filter(productModel -> productModel != null && !productModel.trim().isEmpty())
			.collect(Collectors.toList());

		if (allProductModels.isEmpty()) {
			return Collections.emptyList();
		}

		// 3.一次性获取该设备所有已存在的理论产出数据
		List<TheoreticalOutput> existingOutputs = repository.findByDeviceCode(device.getCode());

		// 4. 将已存在的理论产出的产品型号放入Set中，以便快速查找 (O(1) 复杂度)
		Set<String> existingProductModels = existingOutputs.stream()
			.map(TheoreticalOutput::getProductModel)
			.collect(Collectors.toSet());

		// 5. 遍历所有产品型号，判断是否为样品并构建结果列表
		return allProductModels.stream()
			.filter(productModel -> !existingProductModels.contains(productModel))
			.map(productModel -> {
				TheoreticalOutputSample sample = new TheoreticalOutputSample();
				sample.setLineCode(device.getLineId());
				sample.setDeviceCode(device.getCode());
				sample.setProductModel(productModel);
				// 直接在内存中判断，无需再次查询数据库
				sample.setSample(!existingProductModels.contains(productModel));
				return sample;
			})
			.collect(Collectors.toList());
	}

}