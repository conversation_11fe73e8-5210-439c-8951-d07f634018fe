package com.github.cret.web.oee.domain.capacity;

/**
 * 计划产能数据传输对象
 */
public class PlannedCapacityInfo {

	private String track;

	// 所属轨道
	private String trackName;

	private String lineCode;

	private String productModel;

	private String productStartTime;

	private String productEndTime;

	// 运行时间
	private Double runTime;

	private Integer plannedQuantity;

	public String getTrack() {
		return track;
	}

	public void setTrack(String track) {
		this.track = track;
	}

	public String getTrackName() {
		return trackName;
	}

	public void setTrackName(String trackName) {
		this.trackName = trackName;
	}

	public String getLineCode() {
		return lineCode;
	}

	public void setLineCode(String lineCode) {
		this.lineCode = lineCode;
	}

	public String getProductModel() {
		return productModel;
	}

	public void setProductModel(String productModel) {
		this.productModel = productModel;
	}

	public String getProductStartTime() {
		return productStartTime;
	}

	public void setProductStartTime(String productStartTime) {
		this.productStartTime = productStartTime;
	}

	public String getProductEndTime() {
		return productEndTime;
	}

	public void setProductEndTime(String productEndTime) {
		this.productEndTime = productEndTime;
	}

	public Integer getPlannedQuantity() {
		return plannedQuantity;
	}

	public void setPlannedQuantity(Integer plannedQuantity) {
		this.plannedQuantity = plannedQuantity;
	}

	public Double getRunTime() {
		return runTime;
	}

	public void setRunTime(Double runTime) {
		this.runTime = runTime;
	}

}
