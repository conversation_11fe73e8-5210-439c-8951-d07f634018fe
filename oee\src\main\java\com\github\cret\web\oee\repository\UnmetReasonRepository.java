package com.github.cret.web.oee.repository;

import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import com.github.cret.web.oee.document.UnmetReason;

public interface UnmetReasonRepository extends MongoRepository<UnmetReason, String> {

	/**
	 * 根据产线ID和时间范围查询未达成原因 (已调整)
	 * @param lineId 产线ID
	 * @param startTime 开始时间 (yyyy-MM-dd HH:mm:ss格式)
	 * @param endTime 结束时间 (yyyy-MM-dd HH:mm:ss格式)
	 * @return 未达成原因列表
	 */
	@Query("{ " + "'line_id': ?0, " + "'start_work_time': { '$lt': ?2 }, " + // 文档的开始时间 <
																				// 查询的结束时间
			"'end_work_time': { '$gt': ?1 } " + // 文档的结束时间 > 查询的开始时间
			"}")
	List<UnmetReason> findByLineIdAndWorkTimeBetween(String lineId, String startTime, String endTime);

	/**
	 * 根据产线ID列表和时间范围查询未达成原因 (已调整)
	 * @param lineIds 产线ID列表
	 * @param startTime 开始时间 (yyyy-MM-dd HH:mm:ss格式)
	 * @param endTime 结束时间 (yyyy-MM-dd HH:mm:ss格式)
	 * @return 未达成原因列表
	 */
	@Query("{ " + "'line_id': { '$in': ?0 }, " + "'start_work_time': { '$lt': ?2 }, " + // 文档的开始时间
																						// <
																						// 查询的结束时间
			"'end_work_time': { '$gt': ?1 } " + // 文档的结束时间 > 查询的开始时间
			"}")
	List<UnmetReason> findByLineIdInAndWorkTimeBetween(List<String> lineIds, String startTime, String endTime);

}