package com.github.cret.web.oee.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.github.cret.web.oee.document.LineManualEvents;
import com.github.cret.web.oee.domain.event.LineCurrentStatusResponse;
import com.github.cret.web.oee.domain.event.OperatingRecordDto;
import com.github.cret.web.oee.domain.event.OperatingRecordsResponse;
import com.github.cret.web.oee.domain.event.ToggleStatusResponse;
import com.github.cret.web.oee.repository.LineManualEventsRepository;
import com.github.cret.web.oee.service.LineManualEventsService;

@Service
public class LineManualEventsServiceImpl implements LineManualEventsService {

	private final LineManualEventsRepository eventsRepository;

	public LineManualEventsServiceImpl(LineManualEventsRepository eventsRepository) {
		this.eventsRepository = eventsRepository;
	}

	@Override
	public ToggleStatusResponse toggleLineStatus(String lineCode) {
		Optional<LineManualEvents> latestEventOpt = eventsRepository.findTopByLineCodeOrderByStartTimeDesc(lineCode);

		// 情况一: 线体当前是 "stopped" 状态 (找不到记录，或最新记录的 endTime 不为空)。
		// 此时执行“开线”操作。
		if (latestEventOpt.isEmpty() || latestEventOpt.get().getEndTime() != null) {
			LineManualEvents newEvent = new LineManualEvents();
			newEvent.setLineCode(lineCode);
			newEvent.setStartTime(new Date()); // 开线时间为当前时间
			newEvent.setEndTime(null); // 结束时间为空，表示正在运行
			newEvent.setStatus("running"); // 状态为 running
			newEvent.setSource("manual"); // 数据来源为手动

			eventsRepository.save(newEvent);

			return new ToggleStatusResponse(lineCode, "running");
		}
		// 情况二: 线体当前是 "running" 状态 (最新记录的 endTime 为空)。
		// 此时执行“关线”操作。
		else {
			LineManualEvents runningEvent = latestEventOpt.get();
			runningEvent.setEndTime(new Date()); // 关线时间为当前时间
			runningEvent.setStatus("stopped"); // 状态更新为 stopped

			eventsRepository.save(runningEvent);

			return new ToggleStatusResponse(lineCode, "stopped");
		}
	}

	@Override
	public List<LineCurrentStatusResponse> getCurrentStatusForLines(List<String> lineCodes) {
		return lineCodes.stream().map(this::getSingleLineCurrentStatus).collect(Collectors.toList());
	}

	@Override
	public LineCurrentStatusResponse getCurrentStatus(String lineCode) {
		return getSingleLineCurrentStatus(lineCode);
	}

	/**
	 * 获取单个线体的当前状态
	 * @param lineCode 单个线体编码
	 * @return 该线体的当前状态对象
	 */
	private LineCurrentStatusResponse getSingleLineCurrentStatus(String lineCode) {
		// 查询该线体最新的事件记录
		Optional<LineManualEvents> latestEventOpt = eventsRepository.findTopByLineCodeOrderByStartTimeDesc(lineCode);

		// 使用 Optional 的 map 和 orElse 方法优雅地处理
		return latestEventOpt.map(event -> {
			// 如果最新记录的 endTime 为空，说明正在运行
			if (event.getEndTime() == null) {
				return new LineCurrentStatusResponse(lineCode, "running", event.getStartTime());
			}
			else {
				// 如果 endTime 不为空，说明已停止
				return new LineCurrentStatusResponse(lineCode, "stopped");
			}
		})
			.orElse(
					// 如果找不到任何记录，也视为停止状态
					new LineCurrentStatusResponse(lineCode, "stopped"));
	}

	@Override
	public OperatingRecordsResponse getOperatingRecords(String lineCode, Date queryStartTime, Date queryEndTime) {
		List<LineManualEvents> events = eventsRepository.findOperatingRecordsInDateRange(lineCode, queryStartTime,
				queryEndTime);

		List<OperatingRecordDto> records = events.stream()
			.map(event -> new OperatingRecordDto(event.getStartTime(), event.getEndTime()))
			.collect(Collectors.toList());

		return new OperatingRecordsResponse(lineCode, records);
	}

}
