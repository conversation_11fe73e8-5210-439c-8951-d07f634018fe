package com.github.cret.web.oee.service;

import java.util.Date;
import java.util.List;

import com.github.cret.web.oee.domain.event.LineCurrentStatusResponse;
import com.github.cret.web.oee.domain.event.OperatingRecordsResponse;
import com.github.cret.web.oee.domain.event.ToggleStatusResponse;

public interface LineManualEventsService {

	ToggleStatusResponse toggleLineStatus(String lineCode);

	List<LineCurrentStatusResponse> getCurrentStatusForLines(List<String> lineCodes);

	LineCurrentStatusResponse getCurrentStatus(String lineCode);

	OperatingRecordsResponse getOperatingRecords(String lineCode, Date queryStartTime, Date queryEndTime);

}
