package com.github.cret.web.oee.service;

import java.util.List;
import java.util.Optional;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.UnmetReason;

public interface UnmetReasonService {

	PageList<UnmetReason> page(PageableParam<UnmetReason> param);

	List<UnmetReason> findAll();

	Optional<UnmetReason> findById(String id);

	UnmetReason save(UnmetReason unmetReason);

	UnmetReason update(String id, UnmetReason unmetReason);

	void deleteById(String id);

	void batchDelete(List<String> ids);

}
