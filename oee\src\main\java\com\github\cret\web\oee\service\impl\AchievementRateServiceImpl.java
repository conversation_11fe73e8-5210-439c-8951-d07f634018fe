package com.github.cret.web.oee.service.impl;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.AchievementRate;
import com.github.cret.web.oee.repository.AchievementRateRepository;
import com.github.cret.web.oee.service.AchievementRateService;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AchievementRateServiceImpl implements AchievementRateService {

	private final AchievementRateRepository achievementRateRepository;

	public AchievementRateServiceImpl(AchievementRateRepository achievementRateRepository) {
		this.achievementRateRepository = achievementRateRepository;
	}

	@Override
	public AchievementRate save(AchievementRate achievementRate) {
		return achievementRateRepository.save(achievementRate);
	}

	@Override
	public void delete(String id) {
		achievementRateRepository.deleteById(id);
	}

	@Override
	public List<AchievementRate> findAll() {
		return achievementRateRepository.findAll();
	}

	@Override
	public PageList<AchievementRate> page(PageableParam<AchievementRate> param) {
		Example<AchievementRate> example = Example.of(param.getSearchParams());
		Page<AchievementRate> page = achievementRateRepository.findAll(example, param.getPageRequest());
		return PageList.fromPage(page);
	}

	@Override
	public AchievementRate findById(String id) {
		return achievementRateRepository.findById(id).orElse(null);
	}

	@Override
	public AchievementRate update(String id, AchievementRate achievementRate) {
		achievementRate.setId(id);
		return achievementRateRepository.save(achievementRate);
	}

}
