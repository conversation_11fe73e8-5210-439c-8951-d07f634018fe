package com.github.cret.web.oee.service.impl;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.stereotype.Service;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.UnmetReason;
import com.github.cret.web.oee.repository.UnmetReasonRepository;
import com.github.cret.web.oee.service.UnmetReasonService;

@Service
public class UnmetReasonServiceImpl implements UnmetReasonService {

	private final UnmetReasonRepository unmetReasonRepository;

	public UnmetReasonServiceImpl(UnmetReasonRepository unmetReasonRepository) {
		this.unmetReasonRepository = unmetReasonRepository;
	}

	@Override
	public PageList<UnmetReason> page(PageableParam<UnmetReason> param) {
		ExampleMatcher matcher = ExampleMatcher.matching().withIgnoreNullValues();
		Example<UnmetReason> example = Example.of(param.getSearchParams(), matcher);
		return PageList.fromPage(unmetReasonRepository.findAll(example, param.getPageRequest()));
	}

	@Override
	public List<UnmetReason> findAll() {
		return unmetReasonRepository.findAll();
	}

	@Override
	public Optional<UnmetReason> findById(String id) {
		return unmetReasonRepository.findById(id);
	}

	@Override
	public UnmetReason save(UnmetReason unmetReason) {
		unmetReason.setId(String.format("%s_%s_%s", unmetReason.getLineId(), unmetReason.getStartWorkTime(),
				unmetReason.getEndWorkTime()));
		return unmetReasonRepository.save(unmetReason);
	}

	@Override
	public UnmetReason update(String id, UnmetReason unmetReason) {
		unmetReason.setId(id);
		return unmetReasonRepository.save(unmetReason);
	}

	@Override
	public void deleteById(String id) {
		unmetReasonRepository.deleteById(id);
	}

	@Override
	public void batchDelete(List<String> ids) {
		unmetReasonRepository.deleteAllById(ids);
	}

}
