package com.github.cret.web.oee.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.github.cret.web.oee.document.BadItemDoc;

/**
 * 不良类型字典服务接口
 */
public interface BadItemService {

	/**
	 * 根据不良项目ID查找不良项目信息
	 * @param badItemId 不良项目ID
	 * @return 不良项目信息
	 */
	Optional<BadItemDoc> findByBadItemId(String badItemId);

	/**
	 * 根据不良项目ID列表查找不良项目信息
	 * @param badItemIds 不良项目ID列表
	 * @return 不良项目信息列表
	 */
	List<BadItemDoc> findByBadItemIds(List<String> badItemIds);

	/**
	 * 获取不良项目ID到名称的映射
	 * @param badItemIds 不良项目ID列表
	 * @return ID到名称的映射
	 */
	Map<String, String> getBadItemIdToNameMap(List<String> badItemIds);

	/**
	 * 根据不良类型ID查找不良项目信息
	 * @param badTypeId 不良类型ID
	 * @return 不良项目信息列表
	 */
	List<BadItemDoc> findByBadTypeId(String badTypeId);

	/**
	 * 获取所有不良项目信息
	 * @return 所有不良项目信息列表
	 */
	List<BadItemDoc> findAll();

	/**
	 * 保存不良项目信息
	 * @param badItem 不良项目信息
	 * @return 保存后的不良项目信息
	 */
	BadItemDoc save(BadItemDoc badItem);

	/**
	 * 批量保存不良项目信息
	 * @param badItems 不良项目信息列表
	 * @return 保存后的不良项目信息列表
	 */
	List<BadItemDoc> saveAll(List<BadItemDoc> badItems);

	/**
	 * 根据不良项目名称模糊查询
	 * @param badItemName 不良项目名称关键字
	 * @return 不良项目信息列表
	 */
	List<BadItemDoc> findByBadItemNameContaining(String badItemName);

	/**
	 * 检查不良项目ID是否存在
	 * @param badItemId 不良项目ID
	 * @return 是否存在
	 */
	boolean existsByBadItemId(String badItemId);

	/**
	 * 删除不良项目信息
	 * @param badItemId 不良项目ID
	 */
	void deleteByBadItemId(String badItemId);

	/**
	 * 获取不良类型统计信息
	 * @return 按不良类型ID分组的统计信息
	 */
	Map<String, Long> getBadTypeStatistics();

}
