package com.github.cret.web.oee.controller;

import java.util.List;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.ProductionLine;
import com.github.cret.web.oee.service.ProductionLineService;

/**
 * 线体控制器
 */
@RestController
@RequestMapping("/line")
public class ProductionLineController {

	private final ProductionLineService productionLineService;

	public ProductionLineController(ProductionLineService productionLineService) {
		this.productionLineService = productionLineService;
	}

	@RequestMapping("/list")
	public List<ProductionLine> list() {
		return productionLineService.getList();
	}

	@GetMapping("/listByWorkShopId/{workShopId}")
	public List<ProductionLine> listByWorkShopId(@PathVariable("workShopId") String workShopId) {
		return productionLineService.findProdutionLineByWorkshopCode(workShopId);
	}

	@PostMapping
	public ProductionLine create(@RequestBody ProductionLine productionLine) {
		return productionLineService.createProductionLine(productionLine);
	}

	@PutMapping
	public ProductionLine update(@RequestBody ProductionLine productionLine) {
		return productionLineService.updateProductionLine(productionLine);
	}

	@DeleteMapping("/{id}")
	public void delete(@PathVariable String id) {
		productionLineService.deleteProductionLine(id);
	}

	@PostMapping("/page")
	public PageList<ProductionLine> page(@RequestBody PageableParam<ProductionLine> param) {
		return productionLineService.page(param);
	}

}
