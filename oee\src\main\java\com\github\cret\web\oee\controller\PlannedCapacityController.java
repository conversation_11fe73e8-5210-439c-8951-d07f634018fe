package com.github.cret.web.oee.controller;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.PlannedCapacity;
import com.github.cret.web.oee.service.impl.PlannedCapacityServiceImpl;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 计划产能控制器
 */
@RestController
@RequestMapping("/planned-capacities")
public class PlannedCapacityController {

	private final PlannedCapacityServiceImpl plannedCapacityService;

	public PlannedCapacityController(PlannedCapacityServiceImpl plannedCapacityService) {
		this.plannedCapacityService = plannedCapacityService;
	}

	@PostMapping("/page")
	public PageList<PlannedCapacity> page(@RequestBody PageableParam<PlannedCapacity> param) {
		return plannedCapacityService.page(param);
	}

	@GetMapping
	public List<PlannedCapacity> findAll() {
		return plannedCapacityService.findAll();
	}

	@GetMapping("/{id}")
	public Optional<PlannedCapacity> findById(@PathVariable String id) {
		return plannedCapacityService.findById(id);
	}

	@PostMapping
	public PlannedCapacity save(@RequestBody PlannedCapacity plannedCapacity) {
		return plannedCapacityService.save(plannedCapacity);
	}

	@PutMapping("/{id}")
	public PlannedCapacity update(@PathVariable String id, @RequestBody PlannedCapacity plannedCapacity) {
		return plannedCapacityService.update(id, plannedCapacity);
	}

	@DeleteMapping("/{id}")
	public void deleteById(@PathVariable String id) {
		plannedCapacityService.deleteById(id);
	}

	@DeleteMapping("/batch")
	public void batchDelete(@RequestBody List<String> ids) {
		plannedCapacityService.batchDelete(ids);
	}

	@GetMapping("/template")
	public void downloadTemplate(HttpServletResponse response) throws IOException {
		plannedCapacityService.downloadTemplate(response);
	}

	@PostMapping("/import")
	public Map<String, Object> importFromExcel(@RequestParam("file") MultipartFile file) throws IOException {
		return plannedCapacityService.importFromExcel(file);
	}

	@PostMapping("/export")
	public void exportToExcel(HttpServletResponse response, @RequestBody PlannedCapacity searchParams)
			throws IOException {
		plannedCapacityService.exportToExcel(response, searchParams);
	}

}
