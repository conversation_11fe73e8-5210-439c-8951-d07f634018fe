package com.github.cret.web.oee.service.impl;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.github.cret.web.oee.calculator.HourlyOutputCalculator;
import com.github.cret.web.oee.calculator.HourlyRunTimeCalculator;
import com.github.cret.web.oee.document.AchievementRate;
import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.document.PlannedCapacity;
import com.github.cret.web.oee.document.ProductionLine;
import com.github.cret.web.oee.document.UnmetReason;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.domain.analyze.HourlyOutput;
import com.github.cret.web.oee.domain.analyze.HourlyRunTimeGroup;
import com.github.cret.web.oee.domain.capacity.CapacityProductInfo;
import com.github.cret.web.oee.domain.capacity.CapacityReportDto;
import com.github.cret.web.oee.domain.capacity.CapacityReportWithLine;
import com.github.cret.web.oee.domain.capacity.LineSummary;
import com.github.cret.web.oee.domain.capacity.PlannedCapacityInfo;
import com.github.cret.web.oee.enums.DeviceCategory;
import com.github.cret.web.oee.repository.AchievementRateRepository;
import com.github.cret.web.oee.repository.UnmetReasonRepository;
import com.github.cret.web.oee.service.CapacityReportService;
import com.github.cret.web.oee.service.DeviceService;
import com.github.cret.web.oee.service.ProductionLineService;
import com.github.cret.web.oee.service.TheoreticalOutputService;
import com.github.cret.web.oee.utils.BuilderUtil;

import jakarta.servlet.http.HttpServletResponse;

@Service
public class CapacityReportServiceImpl implements CapacityReportService {

	private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

	private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

	private static final double DEFAULT_ACHIEVEMENT_RATE = 0.95;

	private final ProductionLineService productionLineService;

	private final DeviceService deviceService;

	private final TheoreticalOutputService theoreticalOutputService;

	private final UnmetReasonRepository unmetReasonRepository;

	private final AchievementRateRepository achievementRateRepository;

	private final MongoTemplate mongoTemplate;

	public CapacityReportServiceImpl(ProductionLineService productionLineService, MongoTemplate mongoTemplate,
			DeviceService deviceService, UnmetReasonRepository unmetReasonRepository,
			AchievementRateRepository achievementRateRepository, TheoreticalOutputService theoreticalOutputService) {
		this.productionLineService = productionLineService;
		this.mongoTemplate = mongoTemplate;
		this.deviceService = deviceService;
		this.unmetReasonRepository = unmetReasonRepository;
		this.achievementRateRepository = achievementRateRepository;
		this.theoreticalOutputService = theoreticalOutputService;
	}

	@Override
	public List<CapacityReportWithLine> getCapacityReport(String workDate, int interval) {
		int effectiveInterval = Math.max(interval, 1);

		List<ProductionLine> enabledLines = productionLineService.getList()
			.stream()
			.filter(line -> Objects.equals(line.getEnable(), 1))
			.toList();

		if (enabledLines.isEmpty()) {
			return Collections.emptyList();
		}

		LocalDateTime startDateTime = LocalDate.parse(workDate, DATE_FORMATTER).atTime(8, 0);
		LocalDateTime endDateTime = determineEndDateTime(startDateTime);

		List<String> lineCodes = enabledLines.stream().map(ProductionLine::getCode).toList();
		Map<String, Map<String, Device>> capacityDeviceMapForLines = getCapacityDeviceMapForLines(lineCodes);
		Map<String, Map<Device, List<HourlyRunTimeGroup>>> hourlyRunTimeGroupMap = getHourlyRunTimeGroupMap(
				capacityDeviceMapForLines, workDate);
		Map<String, Map<String, Map<String, Integer>>> allActualCapacities = getActualCapacitiesForLines(
				capacityDeviceMapForLines, workDate);
		Map<String, Map<String, Map<String, Integer>>> allTheoreticalCapacities = getTheoreticalCapacitiesForLines(
				capacityDeviceMapForLines, hourlyRunTimeGroupMap);
		Map<String, Map<String, String>> allUnmetReasons = getUnmetReasonsForLines(lineCodes, startDateTime,
				endDateTime);

		double rate = getRate();

		return enabledLines.parallelStream().map(line -> {
			String lineCode = line.getCode();
			Map<String, Map<String, Integer>> actualCapacities = allActualCapacities.getOrDefault(lineCode,
					Collections.emptyMap());
			Map<String, Map<String, Integer>> theoreticalCapacities = allTheoreticalCapacities.getOrDefault(lineCode,
					Collections.emptyMap());
			Map<String, String> unmetReasons = allUnmetReasons.getOrDefault(lineCode, Collections.emptyMap());
			return buildReportForLine(line, startDateTime, endDateTime, actualCapacities, theoreticalCapacities,
					unmetReasons, rate, effectiveInterval, hourlyRunTimeGroupMap);
		}).collect(Collectors.toList());
	}

	private LocalDateTime determineEndDateTime(LocalDateTime startDateTime) {
		LocalDateTime endDateTime = startDateTime.plusDays(1);
		LocalDateTime now = LocalDateTime.now();
		return endDateTime.isAfter(now) ? now : endDateTime;
	}

	private Map<String, Map<String, Device>> getCapacityDeviceMapForLines(List<String> lineCodes) {
		return lineCodes.parallelStream().collect(Collectors.toMap(Function.identity(), this::getCapacityDeviceMap));
	}

	private Map<String, Device> getCapacityDeviceMap(String lineId) {
		return deviceService.getDevicesByCategory(lineId, DeviceCategory.SMT)
			.stream()
			.collect(Collectors.groupingBy(device -> device.getTrackEnum().getCode(),
					Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(Device::getSort)),
							optionalDevice -> optionalDevice.orElse(null))));
	}

	private Map<String, Map<String, Map<String, Integer>>> getActualCapacitiesForLines(
			Map<String, Map<String, Device>> capacityDevices, String workDate) {
		return capacityDevices.entrySet()
			.parallelStream()
			.collect(Collectors.toMap(Map.Entry::getKey, entry -> getActualCapacities(entry.getValue(), workDate)));
	}

	private Map<String, Map<String, Integer>> getActualCapacities(Map<String, Device> capacityDevice, String workDate) {
		AnalyzeQuery query = BuilderUtil.builder(AnalyzeQuery::new)
			.with(AnalyzeQuery::generateTimeRangeByWorkDate, workDate)
			.build();

		return capacityDevice.values().stream().collect(Collectors.toMap(Device::getCode, device -> {
			List<HourlyOutput> hourlyActualOutput = HourlyOutputCalculator.getHourlyActualOutput(device, query,
					mongoTemplate);
			return hourlyActualOutput.stream()
				.collect(Collectors.toMap(HourlyOutput::getTime, HourlyOutput::getCount, Integer::sum));
		}));
	}

	private Map<String, Map<String, Map<String, Integer>>> getTheoreticalCapacitiesForLines(
			Map<String, Map<String, Device>> capacityDevices,
			Map<String, Map<Device, List<HourlyRunTimeGroup>>> hourlyRunTimeGroupMap) {
		return capacityDevices.entrySet()
			.parallelStream()
			.collect(Collectors.toMap(Map.Entry::getKey,
					entry -> getTheoreticalCapacities(entry.getValue(), hourlyRunTimeGroupMap.get(entry.getKey()))));
	}

	private Map<String, Map<Device, List<HourlyRunTimeGroup>>> getHourlyRunTimeGroupMap(
			Map<String, Map<String, Device>> capacityDevices, String workDate) {
		AnalyzeQuery query = BuilderUtil.builder(AnalyzeQuery::new)
			.with(AnalyzeQuery::generateTimeRangeByWorkDate, workDate)
			.build();

		return capacityDevices.entrySet()
			.parallelStream()
			.collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue()
				.values()
				.stream()
				.collect(Collectors.toMap(Function.identity(),
						device -> HourlyRunTimeCalculator.getHourlyRunTimeGroup(device, query, mongoTemplate)))));
	}

	/**
	 * 获取单条线体的理论产能
	 * @param capacityDevices
	 * @param hourlyRunTimeGroupMap
	 * @return
	 */
	private Map<String, Map<String, Integer>> getTheoreticalCapacities(Map<String, Device> capacityDevices,
			Map<Device, List<HourlyRunTimeGroup>> hourlyRunTimeGroupMap) {
		if (CollectionUtils.isEmpty(hourlyRunTimeGroupMap)) {
			return Collections.emptyMap();
		}

		return capacityDevices.values().stream().collect(Collectors.toMap(Device::getCode, capacityDevice -> {
			List<HourlyRunTimeGroup> hourlyRunTimeGroups = hourlyRunTimeGroupMap.get(capacityDevice);
			if (CollectionUtils.isEmpty(hourlyRunTimeGroups)) {
				return Collections.emptyMap();
			}

			// 获取计划产能
			// Map<String, PlannedCapacity> capacityMap = plannedCapacityRepository
			// .findByLineCodeAndProductModelIn(capacityDevice.getLineId(),
			// hourlyRunTimeGroups.stream().map(HourlyRunTimeGroup::getProductModel).distinct().toList())
			// .stream()
			// .collect(Collectors.toMap(PlannedCapacity::getProductModel,
			// Function.identity()));

			// 获取计划产能
			Map<String, PlannedCapacity> capacityMap = theoreticalOutputService
				.findPlannedCapacityByGrouping(capacityDevice.getLineId(),
						hourlyRunTimeGroups.stream().map(HourlyRunTimeGroup::getProductModel).distinct().toList())
				.stream()
				.collect(Collectors.toMap(PlannedCapacity::getProductModel, Function.identity()));

			return hourlyRunTimeGroups.stream()
				.collect(Collectors.groupingBy(HourlyRunTimeGroup::getHour,
						Collectors.collectingAndThen(
								Collectors.summingDouble(group -> calculateTheoreticalOutput(group, capacityMap)),
								totalOutput -> (int) Math.floor(totalOutput))));
		}));
	}

	/**
	 * 计算每小时理论产出
	 * @param group
	 * @param capacityMap
	 * @return
	 */
	private double calculateTheoreticalOutput(HourlyRunTimeGroup group, Map<String, PlannedCapacity> capacityMap) {
		PlannedCapacity plannedCapacity = capacityMap.get(group.getProductModel());
		if (plannedCapacity != null && plannedCapacity.getPlannedQuantity() != null
				&& plannedCapacity.getPlannedQuantity() > 0) {
			return (double) group.getRunTime() * plannedCapacity.getPlannedQuantity() / 3600.0;
		}
		return 0.0;
	}

	private CapacityReportWithLine buildReportForLine(ProductionLine line, LocalDateTime startDateTime,
			LocalDateTime endDateTime, Map<String, Map<String, Integer>> actualCapacities,
			Map<String, Map<String, Integer>> theoreticalCapacities, Map<String, String> unmetReasons, double rate,
			int interval, Map<String, Map<Device, List<HourlyRunTimeGroup>>> hourlyRunTimeGroupMap) {

		Map<Device, List<HourlyRunTimeGroup>> map = hourlyRunTimeGroupMap.get(line.getCode());

		List<PlannedCapacityInfo> plannedCapacityInfos = getPlannedCapacityInfos(line.getCode(), hourlyRunTimeGroupMap);
		Map<String, Integer> hourlyActualTotal = calculateHourlyTotals(actualCapacities);
		Map<String, Integer> hourlyTheoreticalTotal = calculateHourlyTotals(theoreticalCapacities);

		List<CapacityReportDto> capacityReportDtos = Stream
			.iterate(startDateTime, t -> t.isBefore(endDateTime), t -> t.plusHours(interval))
			.map(intervalStart -> createCapacityReportDto(intervalStart,
					determineIntervalEnd(intervalStart, interval, endDateTime), interval, hourlyActualTotal,
					hourlyTheoreticalTotal, unmetReasons, rate, map))
			.toList();

		LineSummary lineSummary = calculateLineSummary(line.getCode(), capacityReportDtos, rate);

		return BuilderUtil.builder(CapacityReportWithLine::new)
			.with(CapacityReportWithLine::setLineCode, line.getCode())
			.with(CapacityReportWithLine::setCapacityReportDtos, capacityReportDtos)
			.with(CapacityReportWithLine::setLineSummary, lineSummary)
			.with(CapacityReportWithLine::setPlannedCapacityInfos, plannedCapacityInfos)
			.build();
	}

	private Map<String, Integer> calculateHourlyTotals(Map<String, Map<String, Integer>> capacities) {
		return capacities.values()
			.stream()
			.flatMap(m -> m.entrySet().stream())
			.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, Integer::sum));
	}

	private LocalDateTime determineIntervalEnd(LocalDateTime intervalStart, int interval, LocalDateTime endDateTime) {
		LocalDateTime intervalEnd = intervalStart.plusHours(interval);
		return intervalEnd.isAfter(endDateTime) ? endDateTime : intervalEnd;
	}

	private CapacityReportDto createCapacityReportDto(LocalDateTime intervalStart, LocalDateTime intervalEnd,
			int interval, Map<String, Integer> hourlyActualTotal, Map<String, Integer> hourlyTheoreticalTotal,
			Map<String, String> unmetReasons, double rate, Map<Device, List<HourlyRunTimeGroup>> map) {

		int intervalActual = 0;
		int intervalTheoretical = 0;

		for (LocalDateTime currentHourStart = intervalStart; currentHourStart
			.isBefore(intervalEnd); currentHourStart = currentHourStart.plusHours(1)) {
			String currentTimeKey = currentHourStart.format(DATETIME_FORMATTER);
			intervalActual += hourlyActualTotal.getOrDefault(currentTimeKey, 0);
			intervalTheoretical += hourlyTheoreticalTotal.getOrDefault(currentTimeKey, 0);
		}

		double intervalAchievementRate = (intervalTheoretical == 0) ? 0.0
				: (double) intervalActual / intervalTheoretical;
		boolean isMet = intervalAchievementRate >= rate;

		// 构建产品信息列表，汇总该时间段内所有设备的产品型号和运行时间
		List<CapacityProductInfo> capacityProductInfos = buildCapacityProductInfos(intervalStart, intervalEnd, map);

		CapacityReportDto dto = BuilderUtil.builder(CapacityReportDto::new)
			.with(CapacityReportDto::setStartWorkTime, intervalStart.format(DATETIME_FORMATTER))
			.with(CapacityReportDto::setEndWorkTime, intervalEnd.format(DATETIME_FORMATTER))
			.with(CapacityReportDto::setInterval, interval)
			.with(CapacityReportDto::setActualQuantity, intervalActual)
			.with(CapacityReportDto::setTheoreticalQuantity, intervalTheoretical)
			.with(CapacityReportDto::setAchievementRate, String.format("%.2f%%", intervalAchievementRate * 100))
			.with(CapacityReportDto::setStatus, isMet ? "达成" : "未达成")
			.with(CapacityReportDto::setReason, getReason(isMet, intervalStart, intervalEnd, unmetReasons))
			.with(CapacityReportDto::setCapacityProductInfos, capacityProductInfos)
			.build();

		// 自动计算并设置停机时间
		dto.setStopTime(dto.calculateStopTime());

		return dto;
	}

	private String getReason(boolean isMet, LocalDateTime start, LocalDateTime end, Map<String, String> unmetReasons) {
		if (isMet) {
			return "";
		}
		String reasonKey = start.format(DATETIME_FORMATTER) + "-" + end.format(DATETIME_FORMATTER);
		return unmetReasons.getOrDefault(reasonKey, "原因待查");
	}

	private List<PlannedCapacityInfo> getPlannedCapacityInfos(String lineId,
			Map<String, Map<Device, List<HourlyRunTimeGroup>>> runtimeGroupsByDevice) {
		Map<Device, List<HourlyRunTimeGroup>> deviceGroups = runtimeGroupsByDevice.get(lineId);
		if (CollectionUtils.isEmpty(deviceGroups)) {
			return Collections.emptyList();
		}

		List<String> allProductModels = deviceGroups.values()
			.stream()
			.flatMap(List::stream)
			.map(HourlyRunTimeGroup::getProductModel)
			.distinct()
			.toList();

		// Map<String, PlannedCapacity> capacityMap = plannedCapacityRepository
		// .findByLineCodeAndProductModelIn(lineId, allProductModels)
		// .stream()
		// .collect(Collectors.toMap(PlannedCapacity::getProductModel,
		// Function.identity(), (p1, p2) -> p1));

		Map<String, PlannedCapacity> capacityMap = theoreticalOutputService
			.findPlannedCapacityByGrouping(lineId, allProductModels)
			.stream()
			.collect(Collectors.toMap(PlannedCapacity::getProductModel, Function.identity(), (p1, p2) -> p1));

		return deviceGroups.entrySet()
			.stream()
			.flatMap(entry -> createPlannedCapacityInfosForDevice(entry.getKey(), entry.getValue(), capacityMap))
			.collect(Collectors.toList());
	}

	private Stream<PlannedCapacityInfo> createPlannedCapacityInfosForDevice(Device device,
			List<HourlyRunTimeGroup> hourlyRunTimeGroups, Map<String, PlannedCapacity> capacityMap) {
		return hourlyRunTimeGroups.stream().map(HourlyRunTimeGroup::getProductModel).distinct().map(productModel -> {
			PlannedCapacityInfo info = BuilderUtil.builder(PlannedCapacityInfo::new)
				.with(PlannedCapacityInfo::setLineCode, device.getLineId())
				.with(PlannedCapacityInfo::setTrack, device.getTrack())
				.with(PlannedCapacityInfo::setTrackName, device.getTrackEnum().getName())
				.with(PlannedCapacityInfo::setProductModel, productModel)
				.build();
			Optional.ofNullable(capacityMap.get(productModel))
				.ifPresent(pc -> info.setPlannedQuantity(pc.getPlannedQuantity()));
			return info;
		});
	}

	private LineSummary calculateLineSummary(String lineCode, List<CapacityReportDto> dtos, double rate) {
		int totalActualQuantity = dtos.stream().mapToInt(CapacityReportDto::getActualQuantity).sum();
		int totalTheoreticalQuality = dtos.stream().mapToInt(CapacityReportDto::getTheoreticalQuantity).sum();
		long metCount = dtos.stream().filter(dto -> "达成".equals(dto.getStatus())).count();
		double totalAchievementRate = (totalTheoreticalQuality == 0) ? 0.0
				: (double) totalActualQuantity / totalTheoreticalQuality;
		boolean isMet = totalAchievementRate >= rate;
		return BuilderUtil.builder(LineSummary::new)
			.with(LineSummary::setLineCode, lineCode)
			.with(LineSummary::setTotalActualQuantity, totalActualQuantity)
			.with(LineSummary::setTotalTheoreticalQuality, totalTheoreticalQuality)
			.with(LineSummary::setTotalAchievementRate, totalAchievementRate)
			.with(LineSummary::setStatus, isMet)
			.with(LineSummary::setMetCount, (int) metCount)
			.with(LineSummary::setNotMetCount, (int) (dtos.size() - metCount))
			.build();
	}

	private Double getRate() {
		return achievementRateRepository.findAll()
			.stream()
			.findFirst()
			.map(AchievementRate::getRate)
			.orElse(DEFAULT_ACHIEVEMENT_RATE);
	}

	private Map<String, Map<String, String>> getUnmetReasonsForLines(List<String> lineIds, LocalDateTime startTime,
			LocalDateTime endTime) {
		List<UnmetReason> unmetReasons = unmetReasonRepository.findByLineIdInAndWorkTimeBetween(lineIds,
				startTime.format(DATETIME_FORMATTER), endTime.format(DATETIME_FORMATTER));
		return unmetReasons.stream()
			.collect(Collectors.groupingBy(UnmetReason::getLineId,
					Collectors.toMap(unmetReason -> unmetReason.getStartWorkTime() + "-" + unmetReason.getEndWorkTime(),
							UnmetReason::getReason)));
	}

	/**
	 * 构建产品信息列表，汇总该时间段内所有设备的产品型号和运行时间
	 * @param intervalStart 时间段开始时间
	 * @param intervalEnd 时间段结束时间
	 * @param deviceRunTimeMap 设备运行时间数据
	 * @return 汇总后的产品信息列表
	 */
	private List<CapacityProductInfo> buildCapacityProductInfos(LocalDateTime intervalStart, LocalDateTime intervalEnd,
			Map<Device, List<HourlyRunTimeGroup>> deviceRunTimeMap) {
		if (CollectionUtils.isEmpty(deviceRunTimeMap)) {
			return Collections.emptyList();
		}

		// 过滤出时间段内的数据
		List<HourlyRunTimeGroup> filteredGroups = deviceRunTimeMap.values()
			.stream()
			.flatMap(List::stream)
			.filter(group -> isWithinInterval(group.getHour(), intervalStart, intervalEnd))
			.collect(Collectors.toList());

		if (filteredGroups.isEmpty()) {
			return Collections.emptyList();
		}

		// 按产品型号分组，汇总运行时间并保留最早的firstTime
		Map<String, ProductModelSummary> productModelSummaryMap = filteredGroups.stream()
			.collect(Collectors.groupingBy(HourlyRunTimeGroup::getProductModel,
					Collectors.collectingAndThen(Collectors.toList(), groups -> {
						double totalRunTime = groups.stream().mapToDouble(HourlyRunTimeGroup::getRunTime).sum();
						Date earliestFirstTime = groups.stream()
							.map(HourlyRunTimeGroup::getFirstTime)
							.filter(Objects::nonNull)
							.min(Date::compareTo)
							.orElse(null);
						return new ProductModelSummary(totalRunTime, earliestFirstTime);
					})));

		// 将汇总结果转换为CapacityProductInfo列表，按照firstTime从小到大排序
		return productModelSummaryMap.entrySet()
			.stream()
			.map(entry -> BuilderUtil.builder(CapacityProductInfo::new)
				.with(CapacityProductInfo::setProductModel, entry.getKey())
				.with(CapacityProductInfo::setRunTime, entry.getValue().getTotalRunTime())
				.build())
			.sorted((info1, info2) -> {
				ProductModelSummary summary1 = productModelSummaryMap.get(info1.getProductModel());
				ProductModelSummary summary2 = productModelSummaryMap.get(info2.getProductModel());

				// 按照firstTime从小到大排序，null值排在最后
				if (summary1.getEarliestFirstTime() == null && summary2.getEarliestFirstTime() == null) {
					return 0;
				}
				if (summary1.getEarliestFirstTime() == null) {
					return 1;
				}
				if (summary2.getEarliestFirstTime() == null) {
					return -1;
				}
				return summary1.getEarliestFirstTime().compareTo(summary2.getEarliestFirstTime());
			})
			.collect(Collectors.toList());
	}

	/**
	 * 产品型号汇总信息内部类
	 */
	private static class ProductModelSummary {

		private final double totalRunTime;

		private final Date earliestFirstTime;

		public ProductModelSummary(double totalRunTime, Date earliestFirstTime) {
			this.totalRunTime = totalRunTime;
			this.earliestFirstTime = earliestFirstTime;
		}

		public double getTotalRunTime() {
			return totalRunTime;
		}

		public Date getEarliestFirstTime() {
			return earliestFirstTime;
		}

	}

	/**
	 * 判断小时时间是否在指定时间段内
	 * @param hourStr 小时时间字符串
	 * @param intervalStart 时间段开始时间
	 * @param intervalEnd 时间段结束时间
	 * @return 是否在时间段内
	 */
	private boolean isWithinInterval(String hourStr, LocalDateTime intervalStart, LocalDateTime intervalEnd) {
		try {
			LocalDateTime hourTime = LocalDateTime.parse(hourStr, DATETIME_FORMATTER);
			return !hourTime.isBefore(intervalStart) && hourTime.isBefore(intervalEnd);
		}
		catch (Exception e) {
			return false;
		}
	}

	@Override
	public void exportCapacityReport(String workDate, int interval, HttpServletResponse response) {
		List<CapacityReportWithLine> capacityReportWithLines = getCapacityReport(workDate, interval);

		response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
		response.setCharacterEncoding("utf-8");

		try {
			String fileName = URLEncoder.encode("产能报表-" + workDate, "UTF-8").replaceAll("\\+", "%20");
			response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
		}
		catch (UnsupportedEncodingException e) {
			throw new RuntimeException("Failed to encode filename", e);
		}

		try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build()) {
			for (CapacityReportWithLine reportWithLine : capacityReportWithLines) {
				WriteSheet writeSheet = EasyExcel.writerSheet(reportWithLine.getLineCode())
					.head(CapacityReportDto.class)
					.build();
				excelWriter.write(reportWithLine.getCapacityReportDtos(), writeSheet);
			}
		}
		catch (IOException e) {
			throw new UncheckedIOException("Failed to write Excel report", e);
		}
	}

}
