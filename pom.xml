<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.5.4</version>
		<relativePath />
		<!-- lookup parent from repository -->
	</parent>
	<groupId>com.github.cret.web</groupId>
	<artifactId>web-parent</artifactId>
	<version>0.0.1</version>
	<packaging>pom</packaging>
	<modules>
		<module>web-system</module>
		<module>web-security</module>
		<module>web-common</module>
		<module>oee</module>
	</modules>
	<name>web-parent</name>
	<description>Demo project for Spring Boot</description>
	<url />
	<licenses>
		<license />
	</licenses>
	<developers>
		<developer />
	</developers>
	<scm>
		<connection />
		<developerConnection />
		<tag />
		<url />
	</scm>
	<properties>
		<spring.boot.version>3.4.0</spring.boot.version>
		<java.version>17</java.version>
		<project.version>0.1</project.version>
		<org.mapstruct.version>1.6.3</org.mapstruct.version>
		<spring.checkstyle.plugin>0.0.42</spring.checkstyle.plugin>
	</properties>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-bom</artifactId>
				<version>5.8.33</version>
				<type>pom</type>
				<!-- 注意这里是import -->
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>net.logstash.logback</groupId>
				<artifactId>logstash-logback-encoder</artifactId>
				<version>7.4</version>
			</dependency>
			<dependency>
				<groupId>jakarta.validation</groupId>
				<artifactId>jakarta.validation-api</artifactId>
				<version>3.1.0</version>
			</dependency>
			<dependency>
				<groupId>com.github.cret.web</groupId>
				<artifactId>web-system</artifactId>
				<version>0.0.1</version>
			</dependency>
			<dependency>
				<groupId>com.github.cret.web</groupId>
				<artifactId>web-security</artifactId>
				<version>0.0.1</version>
			</dependency>
			<dependency>
				<groupId>com.github.cret.web</groupId>
				<artifactId>web-common</artifactId>
				<version>0.0.1</version>
			</dependency>
			<dependency>
				<groupId>io.minio</groupId>
				<artifactId>minio</artifactId>
				<version>8.5.7</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/org.casbin/casdoor-java-sdk -->
			<dependency>
				<groupId>org.casbin</groupId>
				<artifactId>casdoor-java-sdk</artifactId>
				<version>1.26.0</version>
			</dependency>
			<dependency>
				<groupId>org.mapstruct</groupId>
				<artifactId>mapstruct</artifactId>
				<version>${org.mapstruct.version}</version>
			</dependency>
			<dependency>
				<groupId>io.jsonwebtoken</groupId>
				<artifactId>jjwt-api</artifactId>
				<version>0.12.6</version>
			</dependency>
			<dependency>
				<groupId>io.jsonwebtoken</groupId>
				<artifactId>jjwt-impl</artifactId>
				<version>0.12.6</version>
			</dependency>
			<dependency>
				<groupId>io.jsonwebtoken</groupId>
				<artifactId>jjwt-jackson</artifactId>
				<!-- or jjwt-gson if Gson is preferred -->
				<version>0.12.6</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>runtime</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<version>${spring.boot.version}</version>
			<optional>true</optional>
		</dependency>
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>17</source>
					<!-- depending on your project -->
					<target>17</target>
					<!-- depending on your project -->
					<annotationProcessorPaths>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>${org.mapstruct.version}</version>
						</path>
						<path>
							<groupId>org.springframework.boot</groupId>
							<artifactId>spring-boot-configuration-processor</artifactId>
							<version>${spring.boot.version}</version>
						</path>
						<!-- other annotation processors -->
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<plugin>
				<groupId>io.spring.javaformat</groupId>
				<artifactId>spring-javaformat-maven-plugin</artifactId>
				<version>${spring.checkstyle.plugin}</version>
				<executions>
					<execution>
						<phase>validate</phase>
						<inherited>true</inherited>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>