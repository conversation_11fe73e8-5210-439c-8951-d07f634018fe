package com.github.cret.web.oee.controller;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.service.LabelWorkProcessWithQcBadItemsService;
import com.github.cret.web.oee.task.NgSerialSyncTask;

import cn.hutool.core.date.DateUtil;

/**
 * 定时任务手动调用
 */
@RestController
@RequestMapping("/api/tasks")
public class TaskController {

	private static final Logger log = LoggerFactory.getLogger(TaskController.class);

	private final NgSerialSyncTask ngSerialSyncTask;

	private final LabelWorkProcessWithQcBadItemsService labelWorkProcessWithQcBadItemsService;

	public TaskController(NgSerialSyncTask ngSerialSyncTask,
			LabelWorkProcessWithQcBadItemsService labelWorkProcessWithQcBadItemsService) {
		this.ngSerialSyncTask = ngSerialSyncTask;
		this.labelWorkProcessWithQcBadItemsService = labelWorkProcessWithQcBadItemsService;
	}

	@PostMapping("/sync-ng-serials")
	public ResponseEntity<Map<String, String>> triggerNgSerialSync(@RequestParam(required = false) String startTime,
			@RequestParam(required = false) String endTime) {

		log.info("Received request to trigger NG serial synchronization task with startTime: {} and endTime: {}.",
				startTime, endTime);

		try {
			AnalyzeQuery query = new AnalyzeQuery();
			if (startTime != null) {
				query.setStartTime(DateUtil.parse(startTime));
			}
			if (endTime != null) {
				query.setEndTime(DateUtil.parse(endTime));
			}
			// 调用同步任务
			ngSerialSyncTask.syncNgSerials(query);

			log.info("NG serial sync task has been started successfully.");

			Map<String, String> response = Collections.singletonMap("message",
					"NG serial sync task started successfully.");
			return ResponseEntity.ok(response);

		}
		catch (Exception e) {
			log.error("Failed to execute NG serial sync task due to an unexpected error.", e);

			Map<String, String> errorResponse = Collections.singletonMap("error",
					"Failed to start task. Check server logs for details.");
			return ResponseEntity.internalServerError().body(errorResponse);
		}
	}

	/**
	 * 手动触发同步指定线体的标签工艺过程分析与QC不良项目组合数据
	 * @param lineCode 线体编码
	 * @param mesCode MES线体编码
	 * @param startDate 开始日期时间（格式：yyyy-MM-dd HH:mm:ss）
	 * @param endDate 结束日期时间（格式：yyyy-MM-dd HH:mm:ss）
	 * @return 同步结果
	 */
	@GetMapping("/sync-label-work-process-with-qc-bad-items")
	public ResponseEntity<String> syncLabelWorkProcessWithQcBadItems(@RequestParam String lineCode,
			@RequestParam String mesCode, @RequestParam String startDate, @RequestParam String endDate) {

		try {
			// 解析日期参数
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date start = dateFormat.parse(startDate);
			Date end = dateFormat.parse(endDate);

			// 同步数据
			int syncCount = labelWorkProcessWithQcBadItemsService.syncLabelWorkProcessWithQcBadItems(lineCode, mesCode,
					start, end);

			return ResponseEntity.ok("同步完成，共处理 " + syncCount + " 条数据");
		}
		catch (ParseException e) {
			return ResponseEntity.badRequest().body("日期格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式");
		}
		catch (Exception e) {
			return ResponseEntity.internalServerError().body("同步失败：" + e.getMessage());
		}
	}

	/**
	 * 手动触发同步所有线体当天的标签工艺过程分析与QC不良项目组合数据
	 * @return 同步结果
	 */
	@GetMapping("/sync-all-lines-current-day-with-qc-bad-items")
	public ResponseEntity<String> syncAllLinesCurrentDayWithQcBadItems() {
		try {
			int totalSyncCount = labelWorkProcessWithQcBadItemsService.syncAllLinesCurrentDayData();
			return ResponseEntity.ok("所有线体当天标签工艺过程分析与QC不良项目组合数据同步完成，共同步 " + totalSyncCount + " 条数据");
		}
		catch (Exception e) {
			return ResponseEntity.internalServerError().body("同步失败：" + e.getMessage());
		}
	}

	/**
	 * 手动触发同步所有线体历史的标签工艺过程分析与QC不良项目组合数据（两天前到现在）
	 * @return 同步结果
	 */
	@GetMapping("/sync-all-lines-historical-with-qc-bad-items")
	public ResponseEntity<String> syncAllLinesHistoricalWithQcBadItems() {
		try {
			int totalSyncCount = labelWorkProcessWithQcBadItemsService.syncAllLinesHistoricalData();
			return ResponseEntity.ok("所有线体历史标签工艺过程分析与QC不良项目组合数据同步完成，共同步 " + totalSyncCount + " 条数据");
		}
		catch (Exception e) {
			return ResponseEntity.internalServerError().body("同步失败：" + e.getMessage());
		}
	}

}
