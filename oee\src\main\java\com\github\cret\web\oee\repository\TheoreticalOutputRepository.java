package com.github.cret.web.oee.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import com.github.cret.web.oee.document.analyze.TheoreticalOutput;

@Repository
public interface TheoreticalOutputRepository extends MongoRepository<TheoreticalOutput, String> {

	Optional<TheoreticalOutput> findByDeviceCodeAndProductModel(String deviceCode, String productModel);

	@Query("{ 'deviceCode': { $in: ?0 }, 'productModel': ?1 }")
	List<TheoreticalOutput> findByDeviceCodeInAndProductModel(List<String> deviceCodes, String productModel);

	List<TheoreticalOutput> findByDeviceCode(String deviceCode);

}
