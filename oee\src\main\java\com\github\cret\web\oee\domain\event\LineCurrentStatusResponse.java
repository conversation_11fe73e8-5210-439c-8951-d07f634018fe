package com.github.cret.web.oee.domain.event;

import java.util.Date;

public class LineCurrentStatusResponse {

	private String lineCode;

	private String status;

	private Date startTime; // 仅当 status 为 "running" 时出现

	/**
	 * 用于 "stopped" 状态的构造函数.
	 */
	public LineCurrentStatusResponse(String lineCode, String status) {
		this.lineCode = lineCode;
		this.status = status;
	}

	/**
	 * 用于 "running" 状态的构造函数.
	 */
	public LineCurrentStatusResponse(String lineCode, String status, Date startTime) {
		this.lineCode = lineCode;
		this.status = status;
		this.startTime = startTime;
	}

	// Getters and Setters
	public String getLineCode() {
		return lineCode;
	}

	public void setLineCode(String lineCode) {
		this.lineCode = lineCode;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

}
