package com.github.cret.web.oee.calculator;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.github.cret.web.oee.document.ProductionLine;
import com.github.cret.web.oee.document.analyze.Product;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.domain.analyze.ProductionData;
import com.github.cret.web.oee.service.ProductService;

/**
 * Oee指标计算器
 */
public class OeeCalculator {

	/**
	 * 计算计划时间
	 * @param query
	 * @return 计划时间（秒）
	 */
	public static Double calculatePlanTime(AnalyzeQuery query) {
		// 获取服务器当前UTC时间
		Date now = new Date();

		// 如果开始时间大于当前时间，返回0
		if (query.getStartTime().after(now)) {
			return 0.0;
		}

		// 如果结束时间大于当前时间，使用当前时间作为结束时间
		Date effectiveEndTime = query.getEndTime().after(now) ? now : query.getEndTime();

		// 计算时间差（毫秒）并转换为秒
		long diffMillis = effectiveEndTime.getTime() - query.getStartTime().getTime();
		return Math.max(0, diffMillis / 1000.0); // 确保不返回负值
	}

	/**
	 * 计算实际计划时间
	 * @param lineChangeoverInfo 换线次数
	 * @param calculatePlanTime 计划时间
	 * @return 实际计划时间
	 */
	public static double calculateActualPlanTime(long changeovertime, double calculatePlanTime) {
		return calculatePlanTime - changeovertime;
	}

	/**
	 * 计算运行时间
	 * @param
	 * @return 运行时间（秒）
	 */
	public static Double calculateRunTime(Map<String, List<ProductionData>> productData,
			ProductionLine productionLine) {
		Double result = 0.0;
		switch (productionLine.getType()) {
			case singleTrack:
				for (Map.Entry<String, List<ProductionData>> entry : productData.entrySet()) {
					Double bottleneckRuntime = entry.getValue()
						.stream()
						.filter(data -> data != null && Boolean.TRUE.equals(data.getBottleneck()))
						.map(ProductionData::getRunTime)
						.findFirst()
						.orElse(0.0);
					result += bottleneckRuntime;
				}
				break;

			case dualTrack:
				for (Map.Entry<String, List<ProductionData>> entry : productData.entrySet()) {
					Double bottleneckRuntime = entry.getValue()
						.stream()
						.filter(data -> data != null && Boolean.TRUE.equals(data.getBottleneck()))
						.map(ProductionData::getRunTime)
						.findFirst()
						.orElse(0.0);
					result += bottleneckRuntime;
				}
				break;

			default:
				break;
		}

		return result;
	}

	/**
	 * 计算停机时间，依据将瓶颈工序的停机时间相加
	 * @param query 查询条件
	 * @return 停机时间（秒）
	 */
	public static Double calculateStopTime(Map<String, List<ProductionData>> productData,
			ProductionLine productionLine) {
		Double result = 0.0;
		switch (productionLine.getType()) {
			case singleTrack:
				for (Map.Entry<String, List<ProductionData>> entry : productData.entrySet()) {
					Double bottleneckStopTime = entry.getValue()
						.stream()
						.filter(data -> data != null && Boolean.TRUE.equals(data.getBottleneck()))
						.map(ProductionData::getStopTime)
						.findFirst()
						.orElse(0.0);
					result += bottleneckStopTime;
				}
				break;

			case dualTrack:
				// for (Map.Entry<String, List<ProductionData>> entry :
				// productData.entrySet()) {
				// List<ProductionData> dataList = entry.getValue();

				// ProductionData bottleneckData = dataList.stream()
				// .filter(data -> data != null &&
				// Boolean.TRUE.equals(data.getBottleneck()))
				// .findFirst()
				// .orElse(null);

				// if (bottleneckData != null) {
				// Double bottleneckRuntime = bottleneckData.getRunTime();
				// Double pairedRuntime = dataList.stream()
				// .filter(data -> data != null &&
				// !Boolean.TRUE.equals(data.getBottleneck())
				// && data.getDeviceGroup() != null
				// && data.getDeviceGroup().equals(bottleneckData.getDeviceGroup()))
				// .map(ProductionData::getRunTime)
				// .findFirst()
				// .orElse(0.0);

				// // Calculate stop time as: totalTime - (bottleneckRuntime +
				// // pairedRuntime)
				// Double totalTime = bottleneckData.getTotalTime();
				// Double productStopTime = totalTime - (bottleneckRuntime +
				// pairedRuntime);
				// result += Math.max(0.0, productStopTime); // Ensure non-negative
				// // value
				// }
				// }
				for (Map.Entry<String, List<ProductionData>> entry : productData.entrySet()) {
					Double bottleneckRuntime = entry.getValue()
						.stream()
						.filter(data -> data != null && Boolean.TRUE.equals(data.getBottleneck()))
						.map(ProductionData::getStopTime)
						.findFirst()
						.orElse(0.0);
					result += bottleneckRuntime;
				}
				break;

			default:
				break;
		}

		return result;
	}

	/**
	 * 计算实际生产数量（遍历累加各生产单元的瓶颈工序产量）
	 */
	public static Integer calculateActualProduction(Map<String, List<ProductionData>> productionDatas,
			ProductionLine productionLine) {

		int actualProductionTotal = 0;

		// 遍历每个生产单元的数据组
		for (List<ProductionData> productionData : productionDatas.values()) {
			// 在每个数据组中查找瓶颈工序
			ProductionData bottleneck = null;
			for (ProductionData data : productionData) {
				if (Boolean.TRUE.equals(data.getBottleneck())) {
					bottleneck = data;
					break; // 找到第一个瓶颈后立即跳出
				}
			}

			// 累加有效产量
			if (bottleneck != null) {
				int groupProduction = bottleneck.getModuleNum();

				// 查找同一个deviceGroup的设备，将产能也加上
				if (bottleneck.getDeviceGroup() != null) {
					for (ProductionData data : productionData) {
						// 跳过瓶颈设备本身，查找同组的其他设备
						if (!Boolean.TRUE.equals(data.getBottleneck())
								&& bottleneck.getDeviceGroup().equals(data.getDeviceGroup())) {
							groupProduction += data.getModuleNum();
						}
					}
				}

				actualProductionTotal += groupProduction;
			}
		}

		return actualProductionTotal;
	}

	/**
	 * 计算实际理论数量，依据将瓶颈工序的总时间除以理论ct
	 */
	public static Integer calculatePlanProduction(Map<String, List<ProductionData>> productDatas,
			ProductionLine productionLine) {
		Integer result = 0;
		for (Map.Entry<String, List<ProductionData>> entry : productDatas.entrySet()) {
			List<ProductionData> productionDataList = entry.getValue();

			// 找出瓶颈工序
			ProductionData bottleneck = productionDataList.stream()
				.filter(data -> data != null && Boolean.TRUE.equals(data.getBottleneck()))
				.findFirst()
				.orElse(null);

			if (bottleneck != null) {
				// 计算瓶颈工序的理论产量
				Integer bottleneckProduction = calculateSingleDeviceProduction(bottleneck);

				// 查找同一个deviceGroup的设备，将产能也加上
				if (bottleneck.getDeviceGroup() != null) {
					for (ProductionData data : productionDataList) {
						// 跳过瓶颈设备本身，查找同组的其他设备
						if (!Boolean.TRUE.equals(data.getBottleneck())
								&& bottleneck.getDeviceGroup().equals(data.getDeviceGroup())) {
							bottleneckProduction += calculateSingleDeviceProduction(data);
						}
					}
				}

				result += bottleneckProduction;
			}
		}

		return result;
	}

	/**
	 * 计算单个设备的理论产量
	 */
	private static Integer calculateSingleDeviceProduction(ProductionData data) {
		// 生产板数
		Integer moduleNum = data.getModuleNum();
		// 总时间
		Double totalTime = data.getTotalTime();
		// 理论CT
		Double theoreticalCT = data.getTheoreticalCt();
		Integer flatNum = data.getFlatNum();

		if (totalTime != null && theoreticalCT != null && theoreticalCT > 0) {
			return (int) Math.floor(totalTime / theoreticalCT) * flatNum;
		}
		return moduleNum;
	}

	/**
	 * 计算实际理论数量，依据将瓶颈工序的总时间除以(线体的理论ct最大的)
	 */
	public static Integer calculatePlanProduction(Map<String, List<ProductionData>> productDatas,
			ProductionLine productionLine, Map<String, Double> productMaxCtMap) {
		Integer result = 0;
		for (Map.Entry<String, List<ProductionData>> entry : productDatas.entrySet()) {
			List<ProductionData> productionDataList = entry.getValue();

			// 找出瓶颈工序
			ProductionData bottleneck = productionDataList.stream()
				.filter(data -> data != null && Boolean.TRUE.equals(data.getBottleneck()))
				.findFirst()
				.orElse(null);

			if (bottleneck != null) {
				// 计算瓶颈工序的理论产量
				Integer bottleneckProduction = calculateSingleDeviceProductionWithMaxCt(bottleneck, entry.getKey(),
						productMaxCtMap);

				// 查找同一个deviceGroup的设备，将产能也加上
				if (bottleneck.getDeviceGroup() != null) {
					for (ProductionData data : productionDataList) {
						// 跳过瓶颈设备本身，查找同组的其他设备
						if (!Boolean.TRUE.equals(data.getBottleneck())
								&& bottleneck.getDeviceGroup().equals(data.getDeviceGroup())) {
							bottleneckProduction += calculateSingleDeviceProductionWithMaxCt(data, entry.getKey(),
									productMaxCtMap);
						}
					}
				}

				result += bottleneckProduction;
			}
		}

		return result;
	}

	/**
	 * 使用最大CT计算单个设备的理论产量
	 */
	private static Integer calculateSingleDeviceProductionWithMaxCt(ProductionData data, String productKey,
			Map<String, Double> productMaxCtMap) {
		// 生产板数
		Integer moduleNum = data.getModuleNum();
		// 总时间
		Double totalTime = data.getTotalTime();
		// 使用线体的理论ct最大的
		Double theoreticalCT = productMaxCtMap.get(productKey);
		Integer flatNum = data.getFlatNum();

		if (totalTime != null && theoreticalCT != null && theoreticalCT > 0) {
			return (int) Math.floor(totalTime / theoreticalCT) * flatNum;
		}
		return moduleNum;
	}

	/**
	 * 计算实际生产点数
	 * @param productData 生产数据
	 * @param productService
	 * @return
	 */
	public static Integer calculateActualProuductionPoints(Map<String, List<ProductionData>> productData,
			ProductService productService) {
		return productData.values().stream().mapToInt(dataList -> {
			// 获取瓶颈
			ProductionData bottleneckData = dataList.stream()
				.filter(data -> Boolean.TRUE.equals(data.getBottleneck()))
				.findFirst()
				.orElse(null);

			if (bottleneckData == null) {
				return 0;
			}

			Integer pointNum = 0;
			// 点数
			Product product = productService.findOneByProductModel(bottleneckData.getProductModel());
			if (product != null) {
				pointNum = product.getPointNum();
			}

			return bottleneckData.getModuleNum() * pointNum;
		}).sum();
	}

	public static Integer calculatePlanProuductionPoints(Map<String, List<ProductionData>> productData,
			ProductService productService, ProductionLine productionLine) {
		Integer result = 0;
		switch (productionLine.getType()) {
			case singleTrack:
				// 单轨生产线，直接将瓶颈工序的生产数量相加

				for (Map.Entry<String, List<ProductionData>> entry : productData.entrySet()) {
					// 找出瓶颈工序的数据并计算理论产量
					Integer bottleneckProduction = entry.getValue()
						.stream()
						.filter(data -> data != null && Boolean.TRUE.equals(data.getBottleneck()))
						.mapToInt(data -> {
							// 生产板数
							Integer moduleNum = data.getModuleNum();
							// 总时间
							Double totalTime = data.getTotalTime();
							// 理论CT
							Double theoreticalCT = data.getTheoreticalCt();
							Integer flatNum = data.getFlatNum();
							// 点数
							Integer pointNum = 0;
							Product product = productService.findOneByProductModel(data.getProductModel());
							if (product != null) {
								pointNum = product.getPointNum() != null ? product.getPointNum() : 0;
							}

							if (totalTime != null && theoreticalCT != null && theoreticalCT > 0) {
								return (int) Math.floor(totalTime / theoreticalCT) * flatNum * pointNum;
							}
							return moduleNum * pointNum;
						})
						.findFirst()
						.orElse(0);

					result += bottleneckProduction;
				}
				break;
			case dualTrack:

				for (Map.Entry<String, List<ProductionData>> entry : productData.entrySet()) {
					// 找出瓶颈工序的数据并计算理论产量
					Integer bottleneckProduction = entry.getValue()
						.stream()
						.filter(data -> data != null && Boolean.TRUE.equals(data.getBottleneck()))
						.mapToInt(data -> {
							// 生产板数
							Integer moduleNum = data.getModuleNum();
							// 总时间
							Double totalTime = data.getTotalTime();
							// 理论CT
							Double theoreticalCT = data.getTheoreticalCt();
							Integer flatNum = data.getFlatNum();
							// 点数
							Integer pointNum = 0;
							Product product = productService.findOneByProductModel(data.getProductModel());
							if (product != null) {
								pointNum = product.getPointNum() != null ? product.getPointNum() : 0;
							}

							if (totalTime != null && theoreticalCT != null && theoreticalCT > 0) {
								return (int) Math.floor(totalTime / theoreticalCT) * flatNum * pointNum;
							}
							return moduleNum * pointNum;
						})
						.findFirst()
						.orElse(0);

					result += bottleneckProduction;
				}
				break;
		}

		return result;
	}

	/**
	 * 计算稼动率
	 * @param calculateRunTime 运行时间
	 * @param calculateActualStopTime 实际停止时间
	 * @return 稼动率
	 */
	public static double calculateAvailability(double calculateRunTime, double calculateActualStopTime) {
		// 避免除以零的情况
		if (calculateRunTime + calculateActualStopTime == 0) {
			return 0.00;
		}

		// 计算稼动率 = 运行时间 / (运行时间 + 实际停止时间)
		return Math.abs(calculateRunTime / (calculateRunTime + calculateActualStopTime));
	}

	/**
	 * 计算运行效率
	 * @param actualProduction
	 * @param planProduction
	 * @return
	 */
	public static double calculatePerformance(double actualProduction, double planProduction) {
		// 避免除以零的情况
		if (planProduction == 0) {
			return 0.00;
		}

		// 计算实际运行效率（实际产量/理论产量）
		double performance = actualProduction / planProduction;

		return performance;
	}

	/**
	 * 计算OEE
	 * @param performance
	 * @param availability
	 * @param quality
	 * @return
	 */
	public static Double calculateOee(double performance, double availability, double quality) {
		// 计算OEE（性能效率 * 稼动率 * 良品率）
		double oee = performance * availability * quality;

		return oee;
	}

}
