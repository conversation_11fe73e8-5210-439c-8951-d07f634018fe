package com.github.cret.web.oee.repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import com.github.cret.web.oee.document.LineManualEvents;

@Repository
public interface LineManualEventsRepository extends MongoRepository<LineManualEvents, String> {

	/**
	 * 根据线体编码，按开始时间降序查询最新的一个事件。 Spring Data MongoDB 会根据方法名自动实现这个查询。
	 * @param lineCode 线体编码
	 * @return 包含最新手动事件记录的 Optional，如果不存在则为空
	 */
	Optional<LineManualEvents> findTopByLineCodeOrderByStartTimeDesc(String lineCode);

	@Query("{ 'lineCode': ?0, 'endTime': { '$ne': null }, 'startTime': { '$lte': ?2 }, 'endTime': { '$gte': ?1 } }")
	List<LineManualEvents> findOperatingRecordsInDateRange(String lineCode, Date queryStartTime, Date queryEndTime);

}
