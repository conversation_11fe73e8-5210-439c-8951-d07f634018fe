package com.github.cret.web.oee.domain.capacity;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 产能报表数据传输对象
 */
@ExcelIgnoreUnannotated
public class CapacityReportDto {

	@ExcelProperty("开始时间")
	private String startWorkTime;

	@ExcelProperty("结束时间")
	private String endWorkTime;

	// 实际产能
	@ExcelProperty("实际产能")
	private int actualQuantity;

	@ExcelProperty("理论产能")
	private int theoreticalQuantity;

	// 达成率
	@ExcelProperty("达成率")
	private String achievementRate;

	// 达成状态
	@ExcelProperty("达成状态")
	private String status;

	// 其他原因
	@ExcelProperty("其他原因")
	private String reason;

	// 间隔时间
	private int interval;

	private List<CapacityProductInfo> capacityProductInfos;

	@ExcelProperty("停机时间")
	private Double stopTime;

	public String getStartWorkTime() {
		return startWorkTime;
	}

	public void setStartWorkTime(String startWorkTime) {
		this.startWorkTime = startWorkTime;
	}

	public String getEndWorkTime() {
		return endWorkTime;
	}

	public void setEndWorkTime(String endWorkTime) {
		this.endWorkTime = endWorkTime;
	}

	public int getActualQuantity() {
		return actualQuantity;
	}

	public void setActualQuantity(int actualQuantity) {
		this.actualQuantity = actualQuantity;
	}

	public int getTheoreticalQuantity() {
		return theoreticalQuantity;
	}

	public void setTheoreticalQuantity(int theoreticalQuantity) {
		this.theoreticalQuantity = theoreticalQuantity;
	}

	public String getAchievementRate() {
		return achievementRate;
	}

	public void setAchievementRate(String achievementRate) {
		this.achievementRate = achievementRate;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public int getInterval() {
		return interval;
	}

	public void setInterval(int interval) {
		this.interval = interval;
	}

	public List<CapacityProductInfo> getCapacityProductInfos() {
		return capacityProductInfos;
	}

	public void setCapacityProductInfos(List<CapacityProductInfo> capacityProductInfos) {
		this.capacityProductInfos = capacityProductInfos;
	}

	public Double getStopTime() {
		return stopTime;
	}

	public void setStopTime(Double stopTime) {
		this.stopTime = stopTime;
	}

	/**
	 * 计算停机时间.
	 * @return 停机时间（分钟）。如果开始/结束时间无效或解析失败，则返回 0.0。
	 */
	public Double calculateStopTime() {
		// 检查时间字符串是否有效，若无效则直接返回0.0
		if (startWorkTime == null || startWorkTime.isEmpty() || endWorkTime == null || endWorkTime.isEmpty()) {
			return 0.0;
		}

		try {
			// 定义标准的时间格式化器
			final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
			final LocalDateTime startTime = LocalDateTime.parse(startWorkTime, formatter);
			final LocalDateTime endTime = LocalDateTime.parse(endWorkTime, formatter);

			// 使用 Duration.between() 计算时间差，这种方式更具可读性且不易出错
			// 然后将时间差转换为以秒为单位的浮点数
			final double totalDurationHours = Duration.between(startTime, endTime).toMillis() / 1000.0;

			// 使用 Optional 和 Stream API 安全、优雅地计算总运行时间
			// 即使 capacityProductInfos 为 null 或其中元素为 null，也能正常工作
			final double totalRunTimeHours = Optional.ofNullable(capacityProductInfos)
				.orElse(Collections.emptyList()) // 如果列表为null，则替换为空列表
				.stream()
				.filter(info -> info != null && info.getRunTime() != null) // 过滤掉null的info对象和runTime
				.mapToDouble(CapacityProductInfo::getRunTime)
				.sum();

			// 计算停机时间，并使用 Math.max 确保结果不会是负数
			final double calculatedStopTime = totalDurationHours - totalRunTimeHours;
			return Math.max(0.0, calculatedStopTime);

		}
		catch (DateTimeParseException e) {
			// 如果时间字符串格式不正确，捕获特定的解析异常并返回0.0
			// 在实际应用中，此处可以添加日志记录
			return 0.0;
		}
	}

}
