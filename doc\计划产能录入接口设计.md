# 产能报表

## 1. 原始需求描述

1. 系统需要提供功能，以录入每条产线、每一天、每一个小时的计划产能。
2. 系统已经从贴片机设备接入了每条产线、每一天、每一个小时的实际产能数据。
3. 开发一个报表，用于比较同一时间维度（产线、天、小时）的计划与实际产能，并对达标和未达标的情况用不同颜色进行区分。
4. 对于未达标的时段，必须提供功能，让负责人可以补充未完成的原因。
5. 如果系统中未提供每条线的计划产能数据，则使用理论ct进行计算标准产能。

## 2. 需求分析

此需求的核心目标是**可视化计划与实际产能的差距**，并对未达标的情况进行**原因追溯**。

关键点拆解如下：

1. **数据输入 (计划)**：需要一个界面，让生产计划员能够方便地录入**每条产线、每一天、每一个小时**的计划产能数据。
2. **数据集成 (实际)**：需要定义如何访问和整合已有的贴片机实际产能数据。
3. **数据展示 (报表)**：
    * 创建一个报表界面，用户可以通过选择**产线**和**日期**来查询数据。
    * 报表应以小时为单位，并排展示**计划产能**和**实际产能**。
    * 需要一个明确的“状态”列，通过颜色（例如：绿色代表达标，红色代表未达标）来直观反映每小时的完成情况。
4. **原因录入 (追溯)**：对于未达标的时段，系统必须提供一个功能，让负责人可以录入或选择未完成的原因。这个原因需要和具体的产线、日期、小时关联起来。

## 3. 实现方案

### 3.1. 数据模型设计 (数据库)

建议新增两张核心业务表。

**表1：计划产能表 (`planned_capacity`)**

| 字段名 | 数据类型 | 索引 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | `BIGINT` | 主键 | 唯一标识 |
| `line_id` | `VARCHAR(50)` | 是 | 产线ID (关联到产线信息表) |
| `work_date` | `DATE` | 是 | 工作日期 |
| `hour_of_day` | `TINYINT` | 是 | 小时 (0-23) |
| `planned_quantity` | `INT` | | 计划生产数量 |
| `created_at` | `DATETIME` | | 创建时间 |
| `updated_at` | `DATETIME` | | 更新时间 |

**表2：未达成原因表 (`unmet_reason`)**

| 字段名 | 数据类型 | 索引 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | `BIGINT` | 主键 | 唯一标识 |
| `line_id` | `VARCHAR(50)` | 是 | 产线ID |
| `work_date` | `DATE` | 是 | 工作日期 |
| `hour_of_day` | `TINYINT` | 是 | 小时 (0-23) |
| `reason` | `TEXT` | | 未达成原因的详细描述 |
| `user_id` | `VARCHAR(50)` | | 录入原因的用户ID |
| `created_at` | `DATETIME` | | 创建时间 |

**注**：方案假设已存在 `actual_capacity` 表，其结构类似，以便进行数据联结查询。

### 3.2. 后端服务 (API)

后端需提供以下接口支持前端操作。

* **`POST /api/capacity/planned`**
  * **功能**：创建或更新计划产能。支持批量录入。
  * **请求体 (Body)**：
        ```json
        {
          "line_id": "LINE-A",
          "work_date": "2025-07-15",
          "hourly_data": [
            { "hour": 8, "quantity": 1000 },
            { "hour": 9, "quantity": 1200 }
          ]
        }
        ```

* **`GET /api/capacity/report`**
  * **功能**：获取产能报表数据。
  * **查询参数 (Query Params)**： `?line_id=LINE-A&work_date=2025-07-15`
  * **返回数据结构**：
        ```json
        [
          {
            "hour": 8,
            "planned_quantity": 1000,
            "actual_quantity": 1050,
            "achievement_rate": "105%",
            "status": "达标",
            "reason": null
          },
          {
            "hour": 9,
            "planned_quantity": 1200,
            "actual_quantity": 900,
            "achievement_rate": "75%",
            "status": "未达标",
            "reason": "设备故障30分钟"
          }
        ]
        ```

* **`POST /api/capacity/unmet-reason`**
  * **功能**：为未达标的时段添加或更新原因。
  * **请求体 (Body)**：
        ```json
        {
          "line_id": "LINE-A",
          "work_date": "2025-07-15",
          "hour_of_day": 9,
          "reason": "设备故障30分钟"
        }
        ```

### 3.3. 前端界面 (UI)

前端需实现两个主要界面。

* **界面一：计划产能录入**
  * **形式**：提供一个表单，用户选择“产线”和“日期”后，生成24个输入框（对应24小时）用于填写计划产能。
  * **交互**：提供“应用到全部”或模板功能，简化录入。

* **界面二：产能报表展示**
  * **筛选**：提供“产线选择器”和“日期选择器”。
  * **展示**：使用数据表格展示报表数据，包含列：`小时`, `计划产能`, `实际产能`, `达成率`, `状态`, `未达成原因`, `操作`。
  * **样式**：“状态”列根据“达标”/“未达标”显示不同背景色（绿/红）。
  * **操作**：在“操作”列中，为“未达标”的行提供“录入/修改原因”按钮，点击后弹出对话框供用户输入。

## 4. 技术选型建议

* **前端**： `Vue` / `React` + `Ant Design` / `Element Plus`
* **后端**： `Java (Spring Boot)` / `Python (Django/FastAPI)` / `Node.js (Express)`
* **数据库**： `MySQL` / `PostgreSQL`

## 5. 结构细化

### 产能报表Dto数据结构

```java
@Data
public class CapacityReportDto {
    private int hour;
    private int plannedQuantity;
    private int actualQuantity;
    private String achievementRate;
    private String status;
    private String reason;
}
```

### 产能报表查询接口

```java
@GetMapping("/api/capacity/report")
public List<CapacityReportDto> getCapacityReport(@RequestParam String lineId, @RequestParam String workDate) {
    // 查询逻辑
}
```

查询逻辑，1、获取每小时的计划产能。2、获取每小时的实际产能。
查询线体接口获取所有的线体数据，遍历启用的线体，依据线体编码，获取从当日早上8点到第二天早上8点每小时的时间的计划产能。以及实际产能。

### 产能报表查询接口伪代码

```java
/**
 * @brief Get capacity report for a specific production line and date.
 * @param lineId The ID of the production line.
 * @param workDate The date for the report in 'YYYY-MM-DD' format.
 * @return A list of capacity report data transfer objects (DTOs) for each hour.
 */
@GetMapping("/api/capacity/report")
public List<CapacityReportDto> getCapacityReport(@RequestParam String lineId, @RequestParam String workDate) {
    // 1. Initialize a list to store the hourly report data.
    List<CapacityReportDto> report = new ArrayList<>();

    // 2. Define the 24-hour cycle for the report (e.g., from 8 AM on workDate to 7 AM the next day).
    LocalDateTime startDateTime = LocalDate.parse(workDate).atTime(8, 0);
    LocalDateTime endDateTime = startDateTime.plusDays(1);

    // 3. Retrieve all planned capacities for the given line and date range in one query.
    Map<Integer, Integer> plannedCapacities = getPlannedCapacities(lineId, workDate);

    // 4. Retrieve all actual capacities for the given line and date range in one query.
    Map<Integer, Integer> actualCapacities = getActualCapacities(lineId, workDate);

    // 5. Retrieve all unmet reasons for the given line and date range in one query.
    Map<Integer, String> unmetReasons = getUnmetReasons(lineId, workDate);

    // 6. Get the theoretical cycle time (CT) for the product on the line.
    // This is a fallback if planned capacity is not available.
    Integer theoreticalCt = getTheoreticalCt(lineId, workDate);

    // 7. Iterate through each hour of the working day.
    for (LocalDateTime currentHourStart = startDateTime; currentHourStart.isBefore(endDateTime); currentHourStart = currentHourStart.plusHours(1)) {
        int hour = currentHourStart.getHour();

        // 8. Get planned quantity for the current hour.
        // If not explicitly planned, calculate it using theoretical CT.
        int plannedQuantity = plannedCapacities.getOrDefault(hour, theoreticalCt > 0 ? 3600 / theoreticalCt : 0);

        // 9. Get actual quantity for the current hour.
        int actualQuantity = actualCapacities.getOrDefault(hour, 0);

        // 10. Calculate achievement rate and determine status.
        String achievementRate = "0%";
        String status = "未达标"; // "Not Met"
        if (plannedQuantity > 0) {
            double rate = (double) actualQuantity / plannedQuantity;
            achievementRate = String.format("%.0f%%", rate * 100);
            if (rate >= 1.0) {
                status = "达标"; // "Met"
            }
        } else if (actualQuantity > 0) {
            // If there was no plan but there was production, consider it 100%
            achievementRate = "100%";
            status = "达标"; // "Met"
        }


        // 11. Get the reason for not meeting the target, if applicable.
        String reason = null;
        if ("未达标".equals(status)) {
            reason = unmetReasons.get(hour);
        }

        // 12. Create the DTO and add it to the report list.
        CapacityReportDto dto = new CapacityReportDto();
        dto.setHour(hour);
        dto.setPlannedQuantity(plannedQuantity);
        dto.setActualQuantity(actualQuantity);
        dto.setAchievementRate(achievementRate);
        dto.setStatus(status);
        dto.setReason(reason);
        report.add(dto);
    }

    // 13. Return the completed report.
    return report;
}

// Helper function to get planned capacities for a full day.
private Map<Integer, Integer> getPlannedCapacities(String lineId, String workDate) {
    // PSEUDOCODE:
    // SELECT hour_of_day, planned_quantity FROM planned_capacity
    // WHERE line_id = :lineId AND work_date = :workDate
    // Return a map of {hour -> quantity}.
    return new HashMap<>(); // Replace with actual database call
}

// Helper function to get actual capacities for a full day.
private Map<Integer, Integer> getActualCapacities(String lineId, String workDate) {
    // PSEUDOCODE:
    // SELECT HOUR(timestamp), COUNT(*) FROM actual_capacity_log
    // WHERE line_id = :lineId AND DATE(timestamp) is between workDate 8AM and next day 8AM
    // GROUP BY HOUR(timestamp)
    // Return a map of {hour -> quantity}.
    return new HashMap<>(); // Replace with actual database call
}

// Helper function to get unmet reasons for a full day.
private Map<Integer, String> getUnmetReasons(String lineId, String workDate) {
    // PSEUDOCODE:
    // SELECT hour_of_day, reason FROM unmet_reason
    // WHERE line_id = :lineId AND work_date = :workDate
    // Return a map of {hour -> reason}.
    return new HashMap<>(); // Replace with actual database call
}

// Helper function to get theoretical cycle time.
private Integer getTheoreticalCt(String lineId, String workDate) {
    // PSEUDOCODE:
    // 1. Find the product currently running on the line at the given date.
    // 2. SELECT theoretical_ct FROM product_table WHERE product_id = :currentProductId
    // Return the cycle time in seconds.
    return 30; // Example: 30 seconds
}
```