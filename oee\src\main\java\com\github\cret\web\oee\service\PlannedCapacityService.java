package com.github.cret.web.oee.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.web.multipart.MultipartFile;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.PlannedCapacity;

import jakarta.servlet.http.HttpServletResponse;

public interface PlannedCapacityService {

	/**
	 * 分页查询计划产能
	 * @param param 查询条件
	 * @return 分页结果
	 */
	public PageList<PlannedCapacity> page(PageableParam<PlannedCapacity> param);

	public void downloadTemplate(HttpServletResponse response) throws IOException;

	public Map<String, Object> importFromExcel(MultipartFile file) throws IOException;

	void exportToExcel(HttpServletResponse response, PlannedCapacity searchParams) throws IOException;

	public List<PlannedCapacity> findAll();

	public Optional<PlannedCapacity> findById(String id);

	public PlannedCapacity save(PlannedCapacity plannedCapacity);

	public PlannedCapacity update(String id, PlannedCapacity plannedCapacity);

	public void deleteById(String id);

	public void batchDelete(List<String> ids);

	public Optional<PlannedCapacity> findByLineCodeAndProductModel(String lineCode, String productModel);

}
