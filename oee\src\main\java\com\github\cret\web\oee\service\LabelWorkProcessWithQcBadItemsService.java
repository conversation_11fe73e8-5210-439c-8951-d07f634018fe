package com.github.cret.web.oee.service;

import java.util.Date;
import java.util.List;

import com.github.cret.web.oee.document.mes.LabelWorkProcessWithQcBadItemsDoc;
import com.github.cret.web.oee.domain.mes.LabelWorkProcessWithQcBadItems;

/**
 * 标签工艺过程分析与QC不良项目组合数据服务接口
 */
public interface LabelWorkProcessWithQcBadItemsService {

	/**
	 * 根据线体ID和时间范围查询组合数据
	 * @param plId 线体ID
	 * @param startDate 开始时间
	 * @param endDate 结束时间
	 * @return 组合数据列表
	 */
	List<LabelWorkProcessWithQcBadItemsDoc> getLabelWorkProcessWithQcBadItems(String plId, Date startDate,
			Date endDate);

	/**
	 * 同步指定线体的组合数据
	 * @param plId 线体ID
	 * @param startDate 开始时间
	 * @param endDate 结束时间
	 * @return 同步的数据条数
	 */
	int syncLabelWorkProcessWithQcBadItems(String lineCode, String mesCode, Date startDate, Date endDate);

	/**
	 * 同步所有线体当天的组合数据
	 * @return 同步的总数据条数
	 */
	int syncAllLinesCurrentDayData();

	/**
	 * 同步所有线体历史数据（两天前到现在）
	 * @return 同步的总数据条数
	 */
	int syncAllLinesHistoricalData();

	/**
	 * 保存或更新组合数据
	 * @param data 组合数据
	 * @return 保存后的数据
	 */
	LabelWorkProcessWithQcBadItemsDoc saveOrUpdate(LabelWorkProcessWithQcBadItems data);

	/**
	 * 批量保存或更新组合数据
	 * @param dataList 组合数据列表
	 * @return 保存的数据条数
	 */
	int batchSaveOrUpdate(String plId, List<LabelWorkProcessWithQcBadItems> dataList);

	/**
	 * 根据标签ID列表查询组合数据
	 * @param lbIds 标签ID列表
	 * @return 组合数据列表
	 */
	List<LabelWorkProcessWithQcBadItemsDoc> findByLbIds(List<String> lbIds);

	/**
	 * 根据线体ID和关键工序首次结果查询失败的标签
	 * @param plId 线体ID
	 * @param keyWpFirstResult 关键工序首次结果（通常查询"N"表示失败）
	 * @return 组合数据列表
	 */
	List<LabelWorkProcessWithQcBadItemsDoc> findFailedLabels(String plId, String keyWpFirstResult);

}
