package com.github.cret.web.oee.service;

import java.util.List;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.AchievementRate;

public interface AchievementRateService {

	AchievementRate save(AchievementRate achievementRate);

	AchievementRate update(String id, AchievementRate achievementRate);

	void delete(String id);

	List<AchievementRate> findAll();

	PageList<AchievementRate> page(PageableParam<AchievementRate> param);

	// 根据ID查询异常
	AchievementRate findById(String id);

}
