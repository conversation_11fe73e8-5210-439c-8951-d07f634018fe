package com.github.cret.web.oee.controller;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.oee.domain.event.LineCurrentStatusResponse;
import com.github.cret.web.oee.domain.event.OperatingRecordsResponse;
import com.github.cret.web.oee.domain.event.ToggleStatusRequest;
import com.github.cret.web.oee.domain.event.ToggleStatusResponse;
import com.github.cret.web.oee.service.LineManualEventsService;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/line-manual-events")
public class LineManualEventsController {

	private final LineManualEventsService lineManualEventsService;

	public LineManualEventsController(LineManualEventsService lineManualEventsService) {
		this.lineManualEventsService = lineManualEventsService;
	}

	@PostMapping("/toggleStatus")
	public ToggleStatusResponse toggleStatus(@Valid @RequestBody ToggleStatusRequest request) {

		ToggleStatusResponse responseData = lineManualEventsService.toggleLineStatus(request.getLineCode());

		return responseData;
	}

	@GetMapping("/currentStatuses")
	public List<LineCurrentStatusResponse> getCurrentStatus(@RequestParam("lineCodes") String lineCodesStr) {

		// 如果参数为空或只包含空白，则返回空列表
		if (!StringUtils.hasText(lineCodesStr)) {
			return Collections.emptyList();
		}

		// 将逗号分隔的字符串转换为 List<String>
		List<String> lineCodeList = Arrays.stream(lineCodesStr.split(","))
			.map(String::trim) // 去除每个编码前后的空格
			.filter(code -> !code.isEmpty()) // 过滤掉空字符串
			.collect(Collectors.toList());

		// 调用 service 获取数据
		List<LineCurrentStatusResponse> statusData = lineManualEventsService.getCurrentStatusForLines(lineCodeList);

		return statusData;
	}

	@GetMapping("/currentStatus/{lineCode}")
	public LineCurrentStatusResponse getLineCurrentStatus(@PathVariable String lineCode) {
		return lineManualEventsService.getCurrentStatus(lineCode);
	}

	@GetMapping("/operatingRecords")
	public OperatingRecordsResponse getOperatingRecords(@RequestParam("lineCode") String lineCode,
			@RequestParam("queryStartTime") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Date queryStartTime,
			@RequestParam("queryEndTime") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Date queryEndTime) {

		if (queryStartTime.after(queryEndTime)) {
			throw new IllegalArgumentException("queryStartTime must be before queryEndTime.");
		}

		OperatingRecordsResponse responseData = lineManualEventsService.getOperatingRecords(lineCode, queryStartTime,
				queryEndTime);

		return responseData;
	}

}
